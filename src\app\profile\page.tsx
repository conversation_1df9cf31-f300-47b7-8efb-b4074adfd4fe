'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { UsersService } from '@/lib/db/users';
import { QuestionsService } from '@/lib/db/questions';
import { AnalyticsService } from '@/lib/db/analytics';
import { LoadingPage } from '@/components/ui/Loading';
import { QuestionCard } from '@/components/questions/QuestionCard';
import Link from 'next/link';
import { 
  UserIcon, 
  BookmarkIcon, 
  CheckCircleIcon,
  EyeIcon,
  CalendarIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import { formatDate } from '@/lib/utils';

interface UserStats {
  totalBookmarked: number;
  totalSolved: number;
  joinDate: Date;
  lastActive: Date;
  engagementMetrics?: any;
}

export default function ProfilePage() {
  const { user, appUser, loading: authLoading } = useAuth();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [recentQuestions, setRecentQuestions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'bookmarks' | 'solved'>('overview');

  useEffect(() => {
    if (!authLoading && user && appUser) {
      loadUserStats();
      loadRecentActivity();
    }
  }, [authLoading, user, appUser]);

  const loadUserStats = async () => {
    if (!user || !appUser) return;

    try {
      setLoading(true);
      
      // Get basic user stats
      const userStats = await UsersService.getUserStats(user.uid);
      
      // Get engagement metrics
      const engagementMetrics = await AnalyticsService.getUserEngagementMetrics(user.uid);
      
      if (userStats) {
        setStats({
          ...userStats,
          engagementMetrics,
        });
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRecentActivity = async () => {
    if (!appUser) return;

    try {
      // Load recent bookmarked questions (last 5)
      const recentBookmarks = appUser.bookmarkedQuestions.slice(-5);
      const questions = [];
      
      for (const questionId of recentBookmarks) {
        try {
          const question = await QuestionsService.getQuestion(questionId);
          if (question) {
            questions.push(question);
          }
        } catch (error) {
          console.error(`Error loading question ${questionId}:`, error);
        }
      }
      
      setRecentQuestions(questions.reverse()); // Most recent first
    } catch (error) {
      console.error('Error loading recent activity:', error);
    }
  };

  if (authLoading || loading) {
    return <LoadingPage message="Loading your profile..." />;
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <UserIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Sign in to view profile</h1>
          <p className="text-gray-600 mb-6">
            Create an account or sign in to track your progress and view your profile.
          </p>
          <div className="space-x-4">
            <Link
              href="/auth/signin"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/auth/signup"
              className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Sign Up
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const getProgressPercentage = () => {
    if (!stats) return 0;
    const total = stats.totalBookmarked + stats.totalSolved;
    return total > 0 ? (stats.totalSolved / total) * 100 : 0;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 p-4 rounded-full">
                <UserIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {appUser?.displayName || 'User'}
                </h1>
                <p className="text-gray-600">{user.email}</p>
                {stats && (
                  <p className="text-sm text-gray-500 mt-1">
                    Member since {formatDate(stats.joinDate)}
                  </p>
                )}
              </div>
            </div>
            
            {appUser?.role === 'admin' && (
              <div className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                Admin
              </div>
            )}
          </div>
        </div>

        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center">
                <BookmarkIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Bookmarked</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalBookmarked}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Solved</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalSolved}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center">
                <EyeIcon className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Questions Viewed</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.engagementMetrics?.questionsViewed || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center">
                <TrophyIcon className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Progress</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {Math.round(getProgressPercentage())}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Progress Bar */}
        {stats && (stats.totalBookmarked > 0 || stats.totalSolved > 0) && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold text-gray-900">Learning Progress</h3>
              <span className="text-sm text-gray-600">
                {stats.totalSolved} of {stats.totalBookmarked + stats.totalSolved} questions solved
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-green-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage()}%` }}
              />
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', name: 'Overview', icon: UserIcon },
                { id: 'bookmarks', name: 'Bookmarks', icon: BookmarkIcon },
                { id: 'solved', name: 'Solved', icon: CheckCircleIcon },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Recent Activity */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h4>
                  {recentQuestions.length > 0 ? (
                    <div className="space-y-4">
                      {recentQuestions.slice(0, 3).map((question) => (
                        <QuestionCard key={question.id} question={question} compact />
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-600">No recent activity</p>
                  )}
                </div>

                {/* Quick Actions */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Link
                      href="/bookmarks"
                      className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <BookmarkIcon className="h-6 w-6 text-blue-600" />
                      <div>
                        <p className="font-medium text-gray-900">View Bookmarks</p>
                        <p className="text-sm text-gray-600">{stats?.totalBookmarked || 0} saved</p>
                      </div>
                    </Link>

                    <Link
                      href="/physics"
                      className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="bg-blue-100 p-2 rounded-full">
                        <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Practice Physics</p>
                        <p className="text-sm text-gray-600">Continue learning</p>
                      </div>
                    </Link>

                    <Link
                      href="/chemistry"
                      className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="bg-purple-100 p-2 rounded-full">
                        <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                        </svg>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Practice Chemistry</p>
                        <p className="text-sm text-gray-600">Continue learning</p>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'bookmarks' && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-900">Your Bookmarks</h4>
                  <Link
                    href="/bookmarks"
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    View All
                  </Link>
                </div>
                {recentQuestions.length > 0 ? (
                  <div className="space-y-4">
                    {recentQuestions.slice(0, 5).map((question) => (
                      <QuestionCard key={question.id} question={question} compact />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <BookmarkIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-600">No bookmarks yet</p>
                    <Link
                      href="/physics"
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                    >
                      Start exploring questions
                    </Link>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'solved' && (
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Solved Questions</h4>
                <div className="text-center py-8">
                  <CheckCircleIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600">Solved questions tracking coming soon</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
