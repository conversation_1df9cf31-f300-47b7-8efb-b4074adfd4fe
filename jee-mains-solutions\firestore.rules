rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Questions collection - read access for all, write access for admins only
    match /questions/{questionId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Users collection - users can read/write their own data, admins can read all
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if isAdmin();
    }
    
    // User progress collection - users can read/write their own progress
    match /userProgress/{progressId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow read: if isAdmin();
    }
    
    // Analytics collection - write access for authenticated users, read for admins
    match /analytics/{analyticsId} {
      allow create: if request.auth != null;
      allow read: if isAdmin();
    }
    
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
