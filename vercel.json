{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["iad1"], "env": {"NEXT_PUBLIC_FIREBASE_API_KEY": "@firebase_api_key", "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN": "@firebase_auth_domain", "NEXT_PUBLIC_FIREBASE_PROJECT_ID": "@firebase_project_id", "NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET": "@firebase_storage_bucket", "NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "@firebase_messaging_sender_id", "NEXT_PUBLIC_FIREBASE_APP_ID": "@firebase_app_id", "NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID": "@firebase_measurement_id", "NEXT_PUBLIC_GA_TRACKING_ID": "@ga_tracking_id"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}]}