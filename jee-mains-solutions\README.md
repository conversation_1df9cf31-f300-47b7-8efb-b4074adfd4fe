# JEE Mains Solutions

A comprehensive web platform for JEE Mains question-wise solutions with step-by-step explanations and video tutorials for Physics, Chemistry, and Mathematics.

## Features

- **Comprehensive Question Bank**: 1500+ JEE Mains questions from previous years
- **Step-by-Step Solutions**: Detailed mathematical solutions with LaTeX rendering
- **Video Explanations**: YouTube video tutorials for visual learning
- **Subject Organization**: Questions organized by Physics, Chemistry, and Mathematics
- **Advanced Search & Filters**: Filter by year, shift, topic, difficulty level
- **User Authentication**: Sign up/sign in with progress tracking
- **Bookmarking System**: Save important questions for later review
- **Progress Tracking**: Monitor solved/unsolved questions
- **Admin Panel**: Content management system for adding/editing questions
- **Responsive Design**: Mobile-friendly interface
- **SEO Optimized**: Proper meta tags and sitemaps

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Math Rendering**: KaTeX (react-katex)
- **Icons**: Heroicons
- **Deployment**: Vercel

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Firebase project

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd jee-mains-solutions
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure Firebase:
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Firestore Database
   - Enable Authentication (Email/Password)
   - Get your Firebase config and update `.env.local`:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id
NEXT_PUBLIC_GA_TRACKING_ID=your_ga_tracking_id
```

5. Set up Firestore security rules:
```bash
# Copy the rules from firestore.rules to your Firebase project
```

6. Run the development server:
```bash
npm run dev
```

7. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Database Setup

1. **Firestore Collections**:
   - `questions`: Store question data
   - `users`: Store user profiles
   - `userProgress`: Track user progress
   - `analytics`: Store analytics data

2. **Sample Data**:
   - Use the admin panel to add sample questions
   - Create questions with proper LaTeX formatting

3. **Admin User**:
   - Create a user account
   - Manually set the `role` field to `'admin'` in Firestore

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin panel pages
│   ├── auth/              # Authentication pages
│   ├── physics/           # Physics questions page
│   ├── chemistry/         # Chemistry questions page
│   ├── mathematics/       # Mathematics questions page
│   ├── bookmarks/         # User bookmarks page
│   ├── profile/           # User profile page
│   └── about/             # About page
├── components/            # Reusable React components
│   ├── layout/           # Layout components
│   ├── providers/        # Context providers
│   ├── questions/        # Question-related components
│   ├── ui/               # UI components
│   └── analytics/        # Analytics components
├── lib/                  # Utility libraries
│   ├── db/               # Database services
│   ├── firebase.ts       # Firebase configuration
│   └── utils.ts          # Utility functions
└── types/                # TypeScript type definitions
```

## Key Components

### Question Management
- **QuestionCard**: Display questions with solutions
- **LaTeX**: Math rendering component
- **YouTubeEmbed**: Video player component

### Database Services
- **QuestionsService**: CRUD operations for questions
- **UsersService**: User management and progress tracking
- **AnalyticsService**: Analytics and metrics

### Authentication
- **AuthProvider**: Firebase authentication context
- **Sign In/Up Pages**: User authentication flows

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Manual Deployment

1. Build the project:
```bash
npm run build
```

2. Deploy the `out` directory to your hosting provider

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, create an issue in the repository.
