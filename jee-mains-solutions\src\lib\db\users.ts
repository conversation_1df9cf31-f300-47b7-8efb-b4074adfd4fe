import {
  collection,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
} from 'firebase/firestore';
import { db } from '../firebase';
import { User, UserProgress } from '@/types';

const USERS_COLLECTION = 'users';
const USER_PROGRESS_COLLECTION = 'userProgress';

export class UsersService {
  // Create or update user profile
  static async createOrUpdateUser(userId: string, userData: Partial<User>) {
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userSnap = await getDoc(userRef);

    if (userSnap.exists()) {
      // Update existing user
      await updateDoc(userRef, {
        ...userData,
        lastLoginAt: new Date(),
      });
    } else {
      // Create new user
      await setDoc(userRef, {
        id: userId,
        bookmarkedQuestions: [],
        solvedQuestions: [],
        role: 'user',
        createdAt: new Date(),
        lastLoginAt: new Date(),
        ...userData,
      });
    }
  }

  // Get user profile
  static async getUser(userId: string): Promise<User | null> {
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userSnap = await getDoc(userRef);

    if (userSnap.exists()) {
      return userSnap.data() as User;
    }
    return null;
  }

  // Add question to bookmarks
  static async bookmarkQuestion(userId: string, questionId: string) {
    const userRef = doc(db, USERS_COLLECTION, userId);
    await updateDoc(userRef, {
      bookmarkedQuestions: arrayUnion(questionId),
    });
  }

  // Remove question from bookmarks
  static async unbookmarkQuestion(userId: string, questionId: string) {
    const userRef = doc(db, USERS_COLLECTION, userId);
    await updateDoc(userRef, {
      bookmarkedQuestions: arrayRemove(questionId),
    });
  }

  // Mark question as solved
  static async markQuestionSolved(userId: string, questionId: string) {
    const userRef = doc(db, USERS_COLLECTION, userId);
    await updateDoc(userRef, {
      solvedQuestions: arrayUnion(questionId),
    });

    // Also update user progress
    await this.updateUserProgress(userId, questionId, 'solved');
  }

  // Update user progress for a question
  static async updateUserProgress(
    userId: string,
    questionId: string,
    status: 'solved' | 'attempted' | 'bookmarked',
    timeSpent?: number
  ) {
    const progressId = `${userId}_${questionId}`;
    const progressRef = doc(db, USER_PROGRESS_COLLECTION, progressId);
    const progressSnap = await getDoc(progressRef);

    if (progressSnap.exists()) {
      const currentData = progressSnap.data() as UserProgress;
      await updateDoc(progressRef, {
        status,
        attempts: currentData.attempts + 1,
        lastAttemptAt: new Date(),
        ...(timeSpent && { timeSpent }),
      });
    } else {
      await setDoc(progressRef, {
        userId,
        questionId,
        status,
        attempts: 1,
        lastAttemptAt: new Date(),
        ...(timeSpent && { timeSpent }),
      });
    }
  }

  // Get user progress for a specific question
  static async getUserProgress(
    userId: string,
    questionId: string
  ): Promise<UserProgress | null> {
    const progressId = `${userId}_${questionId}`;
    const progressRef = doc(db, USER_PROGRESS_COLLECTION, progressId);
    const progressSnap = await getDoc(progressRef);

    if (progressSnap.exists()) {
      return progressSnap.data() as UserProgress;
    }
    return null;
  }

  // Check if user is admin
  static async isAdmin(userId: string): Promise<boolean> {
    const user = await this.getUser(userId);
    return user?.role === 'admin' || false;
  }

  // Get user statistics
  static async getUserStats(userId: string) {
    const user = await this.getUser(userId);
    if (!user) return null;

    return {
      totalBookmarked: user.bookmarkedQuestions.length,
      totalSolved: user.solvedQuestions.length,
      joinDate: user.createdAt,
      lastActive: user.lastLoginAt,
    };
  }
}
