'use client';

import React, { useState } from 'react';
import { Question } from '@/types';
import { MathText } from '@/components/ui/LaTeX';
import { VideoPlayer, VideoModal } from '@/components/ui/YouTubeEmbed';
import { useAuth } from '@/components/providers/AuthProvider';
import { UsersService } from '@/lib/db/users';
import { AnalyticsService } from '@/lib/db/analytics';
import { 
  BookmarkIcon, 
  PlayIcon, 
  EyeIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface QuestionCardProps {
  question: Question;
  showSolution?: boolean;
  compact?: boolean;
  className?: string;
}

export function QuestionCard({ 
  question, 
  showSolution = false, 
  compact = false,
  className = '' 
}: QuestionCardProps) {
  const { user, appUser } = useAuth();
  const [isExpanded, setIsExpanded] = useState(showSolution);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(
    appUser?.bookmarkedQuestions.includes(question.id) || false
  );
  const [isSolved, setIsSolved] = useState(
    appUser?.solvedQuestions.includes(question.id) || false
  );

  const handleToggleExpand = async () => {
    if (!isExpanded) {
      // Track view when expanding
      await AnalyticsService.trackAction(question.id, 'view', user?.uid);
    }
    setIsExpanded(!isExpanded);
  };

  const handleBookmark = async () => {
    if (!user) {
      toast.error('Please sign in to bookmark questions');
      return;
    }

    try {
      if (isBookmarked) {
        await UsersService.unbookmarkQuestion(user.uid, question.id);
        setIsBookmarked(false);
        toast.success('Removed from bookmarks');
      } else {
        await UsersService.bookmarkQuestion(user.uid, question.id);
        await AnalyticsService.trackAction(question.id, 'bookmark', user.uid);
        setIsBookmarked(true);
        toast.success('Added to bookmarks');
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      toast.error('Failed to update bookmark');
    }
  };

  const handleMarkSolved = async () => {
    if (!user) {
      toast.error('Please sign in to track progress');
      return;
    }

    try {
      await UsersService.markQuestionSolved(user.uid, question.id);
      await AnalyticsService.trackAction(question.id, 'solve', user.uid);
      setIsSolved(true);
      toast.success('Marked as solved!');
    } catch (error) {
      console.error('Error marking as solved:', error);
      toast.error('Failed to update progress');
    }
  };

  const handleVideoPlay = async () => {
    if (question.youtubeUrl) {
      await AnalyticsService.trackAction(question.id, 'video_watch', user?.uid);
      setIsVideoModalOpen(true);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-600 bg-green-100';
      case 'Medium': return 'text-yellow-600 bg-yellow-100';
      case 'Hard': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSubjectColor = (subject: string) => {
    switch (subject) {
      case 'Physics': return 'text-blue-600 bg-blue-100';
      case 'Chemistry': return 'text-purple-600 bg-purple-100';
      case 'Mathematics': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (compact) {
    return (
      <div className={cn('bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow', className)}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-sm font-medium text-gray-900">Q{question.questionNumber}</span>
              <span className={cn('px-2 py-1 rounded-full text-xs font-medium', getSubjectColor(question.subject))}>
                {question.subject}
              </span>
              <span className={cn('px-2 py-1 rounded-full text-xs font-medium', getDifficultyColor(question.difficulty))}>
                {question.difficulty}
              </span>
            </div>
            <p className="text-sm text-gray-600 line-clamp-2">{question.topic}</p>
          </div>
          <div className="flex items-center space-x-2 ml-4">
            {isSolved && <CheckCircleIcon className="h-5 w-5 text-green-500" />}
            {isBookmarked && <BookmarkSolidIcon className="h-5 w-5 text-blue-500" />}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={cn('bg-white border border-gray-200 rounded-lg shadow-sm question-card', className)}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-3">
                <h3 className="text-lg font-semibold text-gray-900">
                  Question {question.questionNumber}
                </h3>
                <span className={cn('px-3 py-1 rounded-full text-sm font-medium', getSubjectColor(question.subject))}>
                  {question.subject}
                </span>
                <span className={cn('px-3 py-1 rounded-full text-sm font-medium', getDifficultyColor(question.difficulty))}>
                  {question.difficulty}
                </span>
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                <span>JEE Main {question.year} - Shift {question.shift}</span>
                <span>•</span>
                <span>{question.topic}</span>
                <span>•</span>
                <div className="flex items-center space-x-1">
                  <EyeIcon className="h-4 w-4" />
                  <span>{question.viewCount} views</span>
                </div>
              </div>

              <div className="prose prose-sm max-w-none">
                <MathText>{question.questionText}</MathText>
              </div>

              {question.options && (
                <div className="mt-4 space-y-2">
                  {question.options.map((option, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <span className="text-sm font-medium text-gray-500 mt-0.5">
                        {String.fromCharCode(65 + index)}.
                      </span>
                      <MathText className="text-sm">{option}</MathText>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex flex-col items-end space-y-2 ml-6">
              <button
                onClick={handleBookmark}
                className={cn(
                  'p-2 rounded-full transition-colors',
                  isBookmarked 
                    ? 'text-blue-600 hover:text-blue-700' 
                    : 'text-gray-400 hover:text-gray-600'
                )}
                title={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}
              >
                {isBookmarked ? (
                  <BookmarkSolidIcon className="h-5 w-5" />
                ) : (
                  <BookmarkIcon className="h-5 w-5" />
                )}
              </button>

              {!isSolved && (
                <button
                  onClick={handleMarkSolved}
                  className="p-2 rounded-full text-gray-400 hover:text-green-600 transition-colors"
                  title="Mark as solved"
                >
                  <CheckCircleIcon className="h-5 w-5" />
                </button>
              )}

              {isSolved && (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircleIcon className="h-5 w-5" />
                  <span className="text-xs font-medium">Solved</span>
                </div>
              )}
            </div>
          </div>

          <div className="mt-4 flex items-center justify-between">
            <button
              onClick={handleToggleExpand}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
            >
              {isExpanded ? 'Hide Solution' : 'Show Solution'}
            </button>

            {question.youtubeUrl && (
              <button
                onClick={handleVideoPlay}
                className="flex items-center space-x-2 text-red-600 hover:text-red-700 text-sm font-medium transition-colors"
              >
                <PlayIcon className="h-4 w-4" />
                <span>Watch Video</span>
              </button>
            )}
          </div>
        </div>

        {/* Solution */}
        {isExpanded && (
          <div className="p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Solution</h4>
            
            {question.correctAnswer && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm font-medium text-green-800 mb-1">Correct Answer:</p>
                <MathText className="text-green-700">{question.correctAnswer}</MathText>
              </div>
            )}

            <div className="prose prose-sm max-w-none mb-6">
              <MathText>{question.solution}</MathText>
            </div>

            {question.youtubeUrl && (
              <div>
                <h5 className="text-md font-semibold text-gray-900 mb-3">Video Explanation</h5>
                <VideoPlayer 
                  url={question.youtubeUrl} 
                  title={`Question ${question.questionNumber} - ${question.subject}`}
                  className="max-w-2xl"
                />
              </div>
            )}

            {question.tags.length > 0 && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex flex-wrap gap-2">
                  {question.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Video Modal */}
      {question.youtubeUrl && (
        <VideoModal
          isOpen={isVideoModalOpen}
          onClose={() => setIsVideoModalOpen(false)}
          url={question.youtubeUrl}
          title={`Question ${question.questionNumber} - ${question.subject}`}
        />
      )}
    </>
  );
}
