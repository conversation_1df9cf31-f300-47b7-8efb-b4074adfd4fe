import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  Timestamp,
} from 'firebase/firestore';
import { db } from '../firebase';
import { Analytics } from '@/types';

const ANALYTICS_COLLECTION = 'analytics';

export class AnalyticsService {
  // Track user action
  static async trackAction(
    questionId: string,
    action: 'view' | 'bookmark' | 'solve' | 'video_watch',
    userId?: string,
    metadata?: Record<string, any>
  ) {
    try {
      await addDoc(collection(db, ANALYTICS_COLLECTION), {
        questionId,
        action,
        userId: userId || null,
        timestamp: new Date(),
        metadata: metadata || {},
      });
    } catch (error) {
      console.error('Error tracking action:', error);
    }
  }

  // Get question view count
  static async getQuestionViewCount(questionId: string): Promise<number> {
    const q = query(
      collection(db, ANALYTICS_COLLECTION),
      where('questionId', '==', questionId),
      where('action', '==', 'view')
    );

    const snapshot = await getDocs(q);
    return snapshot.size;
  }

  // Get most viewed questions
  static async getMostViewedQuestions(limitCount = 10) {
    const q = query(
      collection(db, ANALYTICS_COLLECTION),
      where('action', '==', 'view'),
      orderBy('timestamp', 'desc'),
      limit(limitCount * 10) // Get more to aggregate
    );

    const snapshot = await getDocs(q);
    const viewCounts: Record<string, number> = {};

    snapshot.docs.forEach(doc => {
      const data = doc.data();
      viewCounts[data.questionId] = (viewCounts[data.questionId] || 0) + 1;
    });

    return Object.entries(viewCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limitCount)
      .map(([questionId, count]) => ({ questionId, viewCount: count }));
  }

  // Get analytics for a specific time period
  static async getAnalyticsByPeriod(
    startDate: Date,
    endDate: Date,
    action?: string
  ) {
    let q = query(
      collection(db, ANALYTICS_COLLECTION),
      where('timestamp', '>=', Timestamp.fromDate(startDate)),
      where('timestamp', '<=', Timestamp.fromDate(endDate))
    );

    if (action) {
      q = query(q, where('action', '==', action));
    }

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Analytics[];
  }

  // Get daily analytics summary
  static async getDailyAnalytics(days = 7) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const analytics = await this.getAnalyticsByPeriod(startDate, endDate);
    
    const dailyStats: Record<string, Record<string, number>> = {};

    analytics.forEach(item => {
      const date = item.timestamp.toISOString().split('T')[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { view: 0, bookmark: 0, solve: 0, video_watch: 0 };
      }
      dailyStats[date][item.action]++;
    });

    return dailyStats;
  }

  // Get subject-wise analytics
  static async getSubjectAnalytics() {
    const q = query(
      collection(db, ANALYTICS_COLLECTION),
      where('action', '==', 'view'),
      orderBy('timestamp', 'desc'),
      limit(1000)
    );

    const snapshot = await getDocs(q);
    const subjectStats: Record<string, number> = {
      Physics: 0,
      Chemistry: 0,
      Mathematics: 0,
    };

    // Note: This would require joining with questions collection
    // For now, we'll return the structure
    return subjectStats;
  }

  // Get user engagement metrics
  static async getUserEngagementMetrics(userId: string) {
    const q = query(
      collection(db, ANALYTICS_COLLECTION),
      where('userId', '==', userId),
      orderBy('timestamp', 'desc')
    );

    const snapshot = await getDocs(q);
    const actions = snapshot.docs.map(doc => doc.data());

    const metrics = {
      totalActions: actions.length,
      questionsViewed: new Set(
        actions.filter(a => a.action === 'view').map(a => a.questionId)
      ).size,
      questionsBookmarked: new Set(
        actions.filter(a => a.action === 'bookmark').map(a => a.questionId)
      ).size,
      questionsSolved: new Set(
        actions.filter(a => a.action === 'solve').map(a => a.questionId)
      ).size,
      videosWatched: actions.filter(a => a.action === 'video_watch').length,
      lastActivity: actions[0]?.timestamp || null,
    };

    return metrics;
  }

  // Clean old analytics data (for maintenance)
  static async cleanOldAnalytics(daysToKeep = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const q = query(
      collection(db, ANALYTICS_COLLECTION),
      where('timestamp', '<', Timestamp.fromDate(cutoffDate))
    );

    const snapshot = await getDocs(q);
    console.log(`Found ${snapshot.size} old analytics records to clean`);
    
    // Note: Batch delete would be implemented here for production
    // This is just a placeholder for the cleanup logic
  }
}
