'use client';

import React, { useState, useEffect } from 'react';
import { QuestionsService } from '@/lib/db/questions';
import { Question, SearchFilters } from '@/types';
import { QuestionCard } from '@/components/questions/QuestionCard';
import { LoadingList, LoadingPage } from '@/components/ui/Loading';
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';

const chemistryTopics = [
  'Atomic Structure',
  'Chemical Bonding',
  'Periodic Table',
  'Chemical Thermodynamics',
  'Chemical Equilibrium',
  'Ionic Equilibrium',
  'Redox Reactions',
  'Electrochemistry',
  'Chemical Kinetics',
  'Surface Chemistry',
  'General Organic Chemistry',
  'Hydrocarbons',
  'Organic Compounds with Functional Groups',
  'Biomolecules',
  'Polymers',
  'Chemistry in Everyday Life',
  'Coordination Compounds',
  'Metallurgy',
  'p-Block Elements',
  's-Block Elements',
  'd-Block and f-Block Elements'
];

const difficulties = ['Easy', 'Medium', 'Hard'];

export default function ChemistryPage() {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedShift, setSelectedShift] = useState<number | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [selectedDifficulty, setSelectedDifficulty] = useState<string | null>(null);
  const [availableYears, setAvailableYears] = useState<{year: number, shift: number}[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    loadQuestions();
    loadAvailableYears();
  }, [selectedYear, selectedShift, selectedTopic, selectedDifficulty]);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      const filters: SearchFilters = {
        subject: 'Chemistry',
        ...(selectedYear && { year: selectedYear }),
        ...(selectedShift && { shift: selectedShift }),
        ...(selectedTopic && { topic: selectedTopic }),
        ...(selectedDifficulty && { difficulty: selectedDifficulty }),
        ...(searchQuery && { searchQuery }),
      };

      const result = await QuestionsService.getQuestions(filters, 50);
      setQuestions(result.questions);
    } catch (error) {
      console.error('Error loading questions:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableYears = async () => {
    try {
      const yearsShifts = await QuestionsService.getAvailableYearsAndShifts();
      setAvailableYears(yearsShifts);
    } catch (error) {
      console.error('Error loading years:', error);
    }
  };

  const handleSearch = () => {
    loadQuestions();
  };

  const clearFilters = () => {
    setSelectedYear(null);
    setSelectedShift(null);
    setSelectedTopic(null);
    setSelectedDifficulty(null);
    setSearchQuery('');
  };

  const uniqueYears = [...new Set(availableYears.map(item => item.year))].sort((a, b) => b - a);
  const shiftsForYear = selectedYear 
    ? availableYears.filter(item => item.year === selectedYear).map(item => item.shift).sort()
    : [];

  if (loading && questions.length === 0) {
    return <LoadingPage message="Loading Chemistry questions..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Chemistry Questions</h1>
          <p className="text-gray-600">
            Comprehensive collection of JEE Mains Chemistry questions with detailed solutions
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          {/* Search Bar */}
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={handleSearch}
              className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
            >
              Search
            </button>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FunnelIcon className="h-5 w-5" />
              <span>Filters</span>
            </button>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="border-t border-gray-200 pt-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Year Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Year</label>
                  <select
                    value={selectedYear || ''}
                    onChange={(e) => {
                      setSelectedYear(e.target.value ? parseInt(e.target.value) : null);
                      setSelectedShift(null);
                    }}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="">All Years</option>
                    {uniqueYears.map(year => (
                      <option key={year} value={year}>JEE Main {year}</option>
                    ))}
                  </select>
                </div>

                {/* Shift Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Shift</label>
                  <select
                    value={selectedShift || ''}
                    onChange={(e) => setSelectedShift(e.target.value ? parseInt(e.target.value) : null)}
                    disabled={!selectedYear}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:bg-gray-100"
                  >
                    <option value="">All Shifts</option>
                    {shiftsForYear.map(shift => (
                      <option key={shift} value={shift}>Shift {shift}</option>
                    ))}
                  </select>
                </div>

                {/* Topic Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Topic</label>
                  <select
                    value={selectedTopic || ''}
                    onChange={(e) => setSelectedTopic(e.target.value || null)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="">All Topics</option>
                    {chemistryTopics.map(topic => (
                      <option key={topic} value={topic}>{topic}</option>
                    ))}
                  </select>
                </div>

                {/* Difficulty Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                  <select
                    value={selectedDifficulty || ''}
                    onChange={(e) => setSelectedDifficulty(e.target.value || null)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="">All Levels</option>
                    {difficulties.map(difficulty => (
                      <option key={difficulty} value={difficulty}>{difficulty}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="mt-4 flex justify-between items-center">
                <button
                  onClick={clearFilters}
                  className="text-gray-600 hover:text-gray-800 text-sm"
                >
                  Clear all filters
                </button>
                <span className="text-sm text-gray-600">
                  {questions.length} question{questions.length !== 1 ? 's' : ''} found
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Questions List */}
        {loading ? (
          <LoadingList count={5} />
        ) : questions.length > 0 ? (
          <div className="space-y-6">
            {questions.map((question) => (
              <QuestionCard key={question.id} question={question} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <MagnifyingGlassIcon className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No questions found</h3>
            <p className="text-gray-600">
              Try adjusting your search criteria or filters to find more questions.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
