import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getAnalytics } from 'firebase/analytics';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "demo-api-key",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "demo-project.firebaseapp.com",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "demo-project",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "demo-project.appspot.com",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "1:123456789:web:abcdef",
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || "G-XXXXXXXXXX",
};

// Initialize Firebase
let app: any;
let db: any;
let auth: any;
let analytics: any;

try {
  app = initializeApp(firebaseConfig);

  // Initialize Firebase services
  db = getFirestore(app);
  auth = getAuth(app);

  // Initialize Analytics (only on client side)
  analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;
} catch (error) {
  console.warn('Firebase initialization failed. Using demo mode.', error);
  // Create mock objects for development
  app = null;
  db = null;
  auth = null;
  analytics = null;
}

export { db, auth, analytics };

export default app;
