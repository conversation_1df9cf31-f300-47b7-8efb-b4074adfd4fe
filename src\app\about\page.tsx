import React from 'react';
import Link from 'next/link';
import { 
  BookOpenIcon, 
  AcademicCapIcon, 
  BeakerIcon, 
  CalculatorIcon,
  UserGroupIcon,
  LightBulbIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    title: 'Comprehensive Question Bank',
    description: 'Access thousands of JEE Mains questions from previous years, organized by subject, year, and shift.',
    icon: BookOpenIcon,
  },
  {
    title: 'Step-by-Step Solutions',
    description: 'Detailed mathematical solutions with proper derivations and conceptual explanations.',
    icon: LightBulbIcon,
  },
  {
    title: 'Video Explanations',
    description: 'Watch expert teachers solve problems with visual demonstrations and tips.',
    icon: AcademicCapIcon,
  },
  {
    title: 'Progress Tracking',
    description: 'Track your learning progress, bookmark important questions, and monitor your performance.',
    icon: ChartBarIcon,
  },
  {
    title: 'Subject-wise Organization',
    description: 'Questions are systematically organized by Physics, Chemistry, and Mathematics.',
    icon: UserGroupIcon,
  },
  {
    title: 'Advanced Search',
    description: 'Find questions by topic, difficulty level, year, or specific keywords.',
    icon: BeakerIcon,
  },
];

const subjects = [
  {
    name: 'Physics',
    topics: ['Mechanics', 'Thermodynamics', 'Optics', 'Electromagnetism', 'Modern Physics'],
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    name: 'Chemistry',
    topics: ['Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Chemical Bonding'],
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
  },
  {
    name: 'Mathematics',
    topics: ['Calculus', 'Algebra', 'Coordinate Geometry', 'Trigonometry', 'Statistics'],
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl">
              About JEE Mains Solutions
            </h1>
            <p className="mt-6 max-w-3xl mx-auto text-xl text-gray-600">
              Your comprehensive platform for JEE Mains preparation with question-wise solutions, 
              video explanations, and progress tracking to help you excel in your exam.
            </p>
          </div>
        </div>
      </div>

      {/* Mission Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900">Our Mission</h2>
          <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
            To provide every JEE aspirant with access to high-quality, comprehensive study materials 
            that make complex concepts easy to understand and help achieve their engineering dreams.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Choose Our Platform?</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <BookOpenIcon className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="text-lg font-semibold text-gray-900">Comprehensive Coverage</h4>
                  <p className="text-gray-600">
                    Complete question bank covering all topics from Physics, Chemistry, and Mathematics.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="bg-green-100 p-2 rounded-full">
                    <AcademicCapIcon className="h-5 w-5 text-green-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="text-lg font-semibold text-gray-900">Expert Solutions</h4>
                  <p className="text-gray-600">
                    Solutions crafted by experienced teachers with detailed explanations and shortcuts.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <ChartBarIcon className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="text-lg font-semibold text-gray-900">Progress Tracking</h4>
                  <p className="text-gray-600">
                    Monitor your learning progress and identify areas that need more attention.
                  </p>
                </div>
              </li>
            </ul>
          </div>
          <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-2xl">
            <div className="text-center">
              <div className="bg-white p-4 rounded-full inline-block mb-4">
                <CalculatorIcon className="h-12 w-12 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">1500+ Questions</h3>
              <p className="text-gray-600 mb-6">
                Carefully curated questions from previous years' JEE Mains papers with detailed solutions.
              </p>
              <Link
                href="/physics"
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Start Practicing
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Platform Features</h2>
            <p className="mt-4 text-lg text-gray-600">
              Everything you need for effective JEE Mains preparation
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                  <div className="bg-blue-100 p-3 rounded-full inline-block mb-4">
                    <Icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Subjects Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900">Subject Coverage</h2>
          <p className="mt-4 text-lg text-gray-600">
            Comprehensive coverage across all JEE Mains subjects
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {subjects.map((subject, index) => (
            <div key={index} className={`${subject.bgColor} p-6 rounded-xl`}>
              <h3 className={`text-xl font-bold ${subject.color} mb-4`}>
                {subject.name}
              </h3>
              <ul className="space-y-2">
                {subject.topics.map((topic, topicIndex) => (
                  <li key={topicIndex} className="text-gray-700 flex items-center">
                    <div className={`w-2 h-2 ${subject.color.replace('text-', 'bg-')} rounded-full mr-3`} />
                    {topic}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white">
              Ready to Start Your JEE Preparation?
            </h2>
            <p className="mt-4 text-xl text-blue-100 max-w-2xl mx-auto">
              Join thousands of students who are already using our platform to improve their JEE Mains scores.
            </p>
            <div className="mt-8 space-x-4">
              <Link
                href="/auth/signup"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                Get Started Free
              </Link>
              <Link
                href="/physics"
                className="border border-white text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Browse Questions
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
