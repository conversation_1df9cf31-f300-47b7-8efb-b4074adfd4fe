import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  DocumentData,
  increment,
} from 'firebase/firestore';
import { db } from '../firebase';
import { Question, SearchFilters } from '@/types';

const QUESTIONS_COLLECTION = 'questions';

export class QuestionsService {
  // Get all questions with optional filters
  static async getQuestions(
    filters: SearchFilters = {},
    limitCount = 20,
    lastDoc?: QueryDocumentSnapshot<DocumentData>
  ) {
    let q = query(collection(db, QUESTIONS_COLLECTION));

    // Apply filters
    if (filters.subject) {
      q = query(q, where('subject', '==', filters.subject));
    }
    if (filters.year) {
      q = query(q, where('year', '==', filters.year));
    }
    if (filters.shift) {
      q = query(q, where('shift', '==', filters.shift));
    }
    if (filters.topic) {
      q = query(q, where('topic', '==', filters.topic));
    }
    if (filters.difficulty) {
      q = query(q, where('difficulty', '==', filters.difficulty));
    }
    if (filters.tags && filters.tags.length > 0) {
      q = query(q, where('tags', 'array-contains-any', filters.tags));
    }

    // Add ordering and pagination
    q = query(q, orderBy('questionNumber'), limit(limitCount));
    
    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    const snapshot = await getDocs(q);
    const questions = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Question[];

    return {
      questions,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === limitCount,
    };
  }

  // Get a single question by ID
  static async getQuestion(id: string): Promise<Question | null> {
    const docRef = doc(db, QUESTIONS_COLLECTION, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Question;
    }
    return null;
  }

  // Add a new question
  static async addQuestion(question: Omit<Question, 'id' | 'createdAt' | 'updatedAt' | 'viewCount' | 'bookmarkCount'>) {
    const now = new Date();
    const questionData = {
      ...question,
      createdAt: now,
      updatedAt: now,
      viewCount: 0,
      bookmarkCount: 0,
    };

    const docRef = await addDoc(collection(db, QUESTIONS_COLLECTION), questionData);
    return docRef.id;
  }

  // Update a question
  static async updateQuestion(id: string, updates: Partial<Question>) {
    const docRef = doc(db, QUESTIONS_COLLECTION, id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date(),
    });
  }

  // Delete a question
  static async deleteQuestion(id: string) {
    const docRef = doc(db, QUESTIONS_COLLECTION, id);
    await deleteDoc(docRef);
  }

  // Increment view count
  static async incrementViewCount(id: string) {
    const docRef = doc(db, QUESTIONS_COLLECTION, id);
    await updateDoc(docRef, {
      viewCount: increment(1),
    });
  }

  // Get questions by year and shift
  static async getQuestionsByYearShift(year: number, shift: number) {
    const q = query(
      collection(db, QUESTIONS_COLLECTION),
      where('year', '==', year),
      where('shift', '==', shift),
      orderBy('questionNumber')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Question[];
  }

  // Search questions by text
  static async searchQuestions(searchQuery: string, filters: SearchFilters = {}) {
    // Note: Firestore doesn't support full-text search natively
    // For production, consider using Algolia or Elasticsearch
    let q = query(collection(db, QUESTIONS_COLLECTION));

    // Apply filters first
    if (filters.subject) {
      q = query(q, where('subject', '==', filters.subject));
    }
    if (filters.year) {
      q = query(q, where('year', '==', filters.year));
    }

    const snapshot = await getDocs(q);
    const questions = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Question[];

    // Client-side text search (for demo purposes)
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      return questions.filter(question =>
        question.questionText.toLowerCase().includes(searchLower) ||
        question.solution.toLowerCase().includes(searchLower) ||
        question.topic.toLowerCase().includes(searchLower) ||
        question.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    return questions;
  }

  // Get unique years and shifts
  static async getAvailableYearsAndShifts() {
    const snapshot = await getDocs(collection(db, QUESTIONS_COLLECTION));
    const yearsShifts = new Set<string>();
    
    snapshot.docs.forEach(doc => {
      const data = doc.data();
      yearsShifts.add(`${data.year}-${data.shift}`);
    });

    const result = Array.from(yearsShifts).map(item => {
      const [year, shift] = item.split('-');
      return { year: parseInt(year), shift: parseInt(shift) };
    });

    return result.sort((a, b) => b.year - a.year || a.shift - b.shift);
  }

  // Get topics by subject
  static async getTopicsBySubject(subject: string) {
    const q = query(
      collection(db, QUESTIONS_COLLECTION),
      where('subject', '==', subject)
    );

    const snapshot = await getDocs(q);
    const topics = new Set<string>();
    
    snapshot.docs.forEach(doc => {
      topics.add(doc.data().topic);
    });

    return Array.from(topics).sort();
  }
}
