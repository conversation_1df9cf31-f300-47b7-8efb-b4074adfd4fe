# Firebase Configuration
# Replace these with your actual Firebase project credentials
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Google Analytics (Optional)
NEXT_PUBLIC_GA_TRACKING_ID=your_ga_tracking_id

# Note: To get these values:
# 1. Go to https://console.firebase.google.com
# 2. Create a new project or select existing one
# 3. Go to Project Settings > General > Your apps
# 4. Add a web app and copy the config values
# 5. Enable Firestore Database and Authentication
