import Link from 'next/link';
import { BookOpenIcon, AcademicCapIcon, BeakerIcon, CalculatorIcon } from '@heroicons/react/24/outline';

const subjects = [
  {
    name: 'Physics',
    href: '/physics',
    icon: AcademicCapIcon,
    color: 'bg-blue-500',
    description: 'Mechanics, Thermodynamics, Optics, Electromagnetism, and Modern Physics',
    questionCount: '500+ Questions'
  },
  {
    name: 'Chemistry',
    href: '/chemistry',
    icon: BeakerIcon,
    color: 'bg-purple-500',
    description: 'Organic, Inorganic, Physical Chemistry, and Chemical Bonding',
    questionCount: '450+ Questions'
  },
  {
    name: 'Mathematics',
    href: '/mathematics',
    icon: CalculatorIcon,
    color: 'bg-green-500',
    description: 'Algebra, Calculus, Coordinate Geometry, Trigonometry, and Statistics',
    questionCount: '600+ Questions'
  }
];

const features = [
  {
    title: 'Step-by-Step Solutions',
    description: 'Detailed explanations with mathematical derivations and conceptual clarity.',
    icon: BookOpenIcon
  },
  {
    title: 'Video Explanations',
    description: 'Watch expert teachers solve problems with visual demonstrations.',
    icon: AcademicCapIcon
  },
  {
    title: 'Year-wise Organization',
    description: 'Questions organized by year and shift for systematic preparation.',
    icon: CalculatorIcon
  }
];

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
              JEE Mains
              <span className="text-blue-600"> Solutions</span>
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-gray-600">
              Comprehensive question-wise solutions with step-by-step explanations and video tutorials
              for Physics, Chemistry, and Mathematics.
            </p>
            <div className="mt-8 flex justify-center space-x-4">
              <Link
                href="/physics"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Start Practicing
              </Link>
              <Link
                href="/about"
                className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Subjects Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900">Choose Your Subject</h2>
          <p className="mt-4 text-lg text-gray-600">
            Access thousands of solved questions organized by subject and year
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {subjects.map((subject) => {
            const Icon = subject.icon;
            return (
              <Link
                key={subject.name}
                href={subject.href}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
              >
                <div className="flex items-center justify-center mb-6">
                  <div className={`${subject.color} p-4 rounded-full`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 text-center mb-3">
                  {subject.name}
                </h3>
                <p className="text-gray-600 text-center mb-4">
                  {subject.description}
                </p>
                <div className="text-center">
                  <span className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                    {subject.questionCount}
                  </span>
                </div>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Why Choose Our Platform?</h2>
            <p className="mt-4 text-lg text-gray-600">
              Everything you need to excel in JEE Mains preparation
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center">
                  <div className="flex items-center justify-center mb-4">
                    <div className="bg-blue-100 p-3 rounded-full">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white">
              Ready to Start Your JEE Preparation?
            </h2>
            <p className="mt-4 text-xl text-blue-100">
              Join thousands of students who have improved their scores with our platform
            </p>
            <div className="mt-8">
              <Link
                href="/auth/signup"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                Get Started Free
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
