'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { QuestionsService } from '@/lib/db/questions';
import { AnalyticsService } from '@/lib/db/analytics';
import { LoadingPage } from '@/components/ui/Loading';
import Link from 'next/link';
import { 
  PlusIcon, 
  PencilIcon, 
  EyeIcon, 
  BookmarkIcon,
  ChartBarIcon,
  UsersIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface DashboardStats {
  totalQuestions: number;
  totalViews: number;
  totalBookmarks: number;
  questionsBySubject: Record<string, number>;
  recentQuestions: any[];
  popularQuestions: any[];
}

export default function AdminDashboard() {
  const { user, isAdmin, loading: authLoading } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!authLoading && isAdmin) {
      loadDashboardStats();
    }
  }, [authLoading, isAdmin]);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      
      // Get questions by subject
      const [physicsResult, chemistryResult, mathResult] = await Promise.all([
        QuestionsService.getQuestions({ subject: 'Physics' }, 1000),
        QuestionsService.getQuestions({ subject: 'Chemistry' }, 1000),
        QuestionsService.getQuestions({ subject: 'Mathematics' }, 1000),
      ]);

      const allQuestions = [
        ...physicsResult.questions,
        ...chemistryResult.questions,
        ...mathResult.questions,
      ];

      // Calculate stats
      const totalViews = allQuestions.reduce((sum, q) => sum + q.viewCount, 0);
      const totalBookmarks = allQuestions.reduce((sum, q) => sum + q.bookmarkCount, 0);

      // Get popular questions
      const popularQuestions = await AnalyticsService.getMostViewedQuestions(5);

      const dashboardStats: DashboardStats = {
        totalQuestions: allQuestions.length,
        totalViews,
        totalBookmarks,
        questionsBySubject: {
          Physics: physicsResult.questions.length,
          Chemistry: chemistryResult.questions.length,
          Mathematics: mathResult.questions.length,
        },
        recentQuestions: allQuestions
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 5),
        popularQuestions: popularQuestions.slice(0, 5),
      };

      setStats(dashboardStats);
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (authLoading) {
    return <LoadingPage message="Checking permissions..." />;
  }

  if (!user || !isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You don't have permission to access the admin panel.</p>
          <Link
            href="/"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return <LoadingPage message="Loading dashboard..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
          <p className="text-gray-600">Manage questions, view analytics, and monitor platform activity</p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link
            href="/admin/questions/new"
            className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-full">
                <PlusIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Add Question</h3>
                <p className="text-gray-600">Create a new question with solution</p>
              </div>
            </div>
          </Link>

          <Link
            href="/admin/questions"
            className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-full">
                <PencilIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Manage Questions</h3>
                <p className="text-gray-600">Edit existing questions and solutions</p>
              </div>
            </div>
          </Link>

          <Link
            href="/admin/analytics"
            className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <div className="bg-purple-100 p-3 rounded-full">
                <ChartBarIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Analytics</h3>
                <p className="text-gray-600">View detailed platform analytics</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Stats Overview */}
        {stats && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <DocumentTextIcon className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Questions</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalQuestions}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <EyeIcon className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Views</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalViews.toLocaleString()}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <BookmarkIcon className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Bookmarks</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalBookmarks.toLocaleString()}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <UsersIcon className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Avg. Views/Question</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats.totalQuestions > 0 ? Math.round(stats.totalViews / stats.totalQuestions) : 0}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Subject Distribution */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Questions by Subject</h3>
                <div className="space-y-4">
                  {Object.entries(stats.questionsBySubject).map(([subject, count]) => (
                    <div key={subject} className="flex items-center justify-between">
                      <span className="text-gray-700">{subject}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              subject === 'Physics' ? 'bg-blue-500' :
                              subject === 'Chemistry' ? 'bg-purple-500' : 'bg-green-500'
                            }`}
                            style={{
                              width: `${(count / Math.max(...Object.values(stats.questionsBySubject))) * 100}%`
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium text-gray-900">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Questions</h3>
                <div className="space-y-3">
                  {stats.recentQuestions.map((question, index) => (
                    <div key={question.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          Q{question.questionNumber} - {question.subject}
                        </p>
                        <p className="text-xs text-gray-600">{question.topic}</p>
                      </div>
                      <Link
                        href={`/admin/questions/${question.id}`}
                        className="text-blue-600 hover:text-blue-700 text-sm"
                      >
                        Edit
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
