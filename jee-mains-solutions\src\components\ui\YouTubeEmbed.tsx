'use client';

import React, { useState } from 'react';
import { PlayIcon } from '@heroicons/react/24/solid';
import { extractYouTubeVideoId } from '@/lib/utils';

interface YouTubeEmbedProps {
  url: string;
  title?: string;
  className?: string;
  autoplay?: boolean;
  showThumbnail?: boolean;
}

export function YouTubeEmbed({ 
  url, 
  title = 'Video Solution', 
  className = '',
  autoplay = false,
  showThumbnail = true 
}: YouTubeEmbedProps) {
  const [isLoaded, setIsLoaded] = useState(!showThumbnail);
  const videoId = extractYouTubeVideoId(url);

  if (!videoId) {
    return (
      <div className={`bg-gray-100 rounded-lg p-4 text-center ${className}`}>
        <p className="text-gray-500">Invalid YouTube URL</p>
      </div>
    );
  }

  const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  const embedUrl = `https://www.youtube.com/embed/${videoId}${autoplay ? '?autoplay=1' : ''}`;

  if (!isLoaded && showThumbnail) {
    return (
      <div className={`relative bg-black rounded-lg overflow-hidden cursor-pointer group ${className}`}>
        <img
          src={thumbnailUrl}
          alt={title}
          className="w-full h-full object-cover transition-opacity group-hover:opacity-80"
          onError={(e) => {
            // Fallback to default thumbnail if maxres doesn't exist
            const target = e.target as HTMLImageElement;
            target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
          }}
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <button
            onClick={() => setIsLoaded(true)}
            className="bg-red-600 hover:bg-red-700 text-white rounded-full p-4 transition-all transform group-hover:scale-110"
            aria-label="Play video"
          >
            <PlayIcon className="h-8 w-8 ml-1" />
          </button>
        </div>
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          <p className="text-white text-sm font-medium">{title}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      <iframe
        src={embedUrl}
        title={title}
        className="w-full h-full"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        loading="lazy"
      />
    </div>
  );
}

interface VideoPlayerProps {
  url?: string;
  title?: string;
  className?: string;
}

export function VideoPlayer({ url, title, className = '' }: VideoPlayerProps) {
  if (!url) {
    return (
      <div className={`bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center ${className}`}>
        <div className="text-gray-400 mb-2">
          <PlayIcon className="h-12 w-12 mx-auto" />
        </div>
        <p className="text-gray-500 text-sm">No video solution available</p>
      </div>
    );
  }

  return (
    <div className={`aspect-video ${className}`}>
      <YouTubeEmbed 
        url={url} 
        title={title}
        className="w-full h-full"
        showThumbnail={true}
      />
    </div>
  );
}

// Component for displaying video in a modal or expanded view
export function VideoModal({ 
  isOpen, 
  onClose, 
  url, 
  title 
}: {
  isOpen: boolean;
  onClose: () => void;
  url: string;
  title?: string;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80">
      <div className="relative w-full max-w-4xl aspect-video">
        <button
          onClick={onClose}
          className="absolute -top-10 right-0 text-white hover:text-gray-300 text-xl font-bold"
        >
          ✕
        </button>
        <YouTubeEmbed 
          url={url} 
          title={title}
          className="w-full h-full"
          autoplay={true}
          showThumbnail={false}
        />
      </div>
    </div>
  );
}

// Utility component for video thumbnail only
export function VideoThumbnail({ 
  url, 
  title, 
  onClick,
  className = '' 
}: {
  url: string;
  title?: string;
  onClick?: () => void;
  className?: string;
}) {
  const videoId = extractYouTubeVideoId(url);
  
  if (!videoId) return null;

  const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;

  return (
    <div 
      className={`relative cursor-pointer group ${className}`}
      onClick={onClick}
    >
      <img
        src={thumbnailUrl}
        alt={title || 'Video thumbnail'}
        className="w-full h-full object-cover rounded-lg transition-opacity group-hover:opacity-80"
      />
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="bg-red-600 hover:bg-red-700 text-white rounded-full p-2 transition-all transform group-hover:scale-110">
          <PlayIcon className="h-6 w-6 ml-0.5" />
        </div>
      </div>
    </div>
  );
}
