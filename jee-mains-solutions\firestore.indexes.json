{"indexes": [{"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subject", "order": "ASCENDING"}, {"fieldPath": "year", "order": "DESCENDING"}, {"fieldPath": "questionNumber", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subject", "order": "ASCENDING"}, {"fieldPath": "topic", "order": "ASCENDING"}, {"fieldPath": "questionNumber", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "year", "order": "ASCENDING"}, {"fieldPath": "shift", "order": "ASCENDING"}, {"fieldPath": "questionNumber", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subject", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "viewCount", "order": "DESCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tags", "arrayConfig": "CONTAINS"}, {"fieldPath": "subject", "order": "ASCENDING"}, {"fieldPath": "questionNumber", "order": "ASCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "questionId", "order": "ASCENDING"}, {"fieldPath": "action", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "action", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "userProgress", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "lastAttemptAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "questions", "fieldPath": "tags", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}]}