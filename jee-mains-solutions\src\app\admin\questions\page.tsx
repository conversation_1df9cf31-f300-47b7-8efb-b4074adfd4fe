'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { QuestionsService } from '@/lib/db/questions';
import { Question, SearchFilters } from '@/types';
import { LoadingPage, LoadingList } from '@/components/ui/Loading';
import { QuestionCard } from '@/components/questions/QuestionCard';
import Link from 'next/link';
import { 
  PlusIcon, 
  MagnifyingGlassIcon, 
  FunnelIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export default function AdminQuestionsPage() {
  const { user, isAdmin, loading: authLoading } = useAuth();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedShift, setSelectedShift] = useState<number | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [selectedDifficulty, setSelectedDifficulty] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [availableYears, setAvailableYears] = useState<{year: number, shift: number}[]>([]);

  const subjects = ['Physics', 'Chemistry', 'Mathematics'];
  const difficulties = ['Easy', 'Medium', 'Hard'];

  useEffect(() => {
    if (!authLoading && isAdmin) {
      loadQuestions();
      loadAvailableYears();
    }
  }, [authLoading, isAdmin, selectedSubject, selectedYear, selectedShift, selectedTopic, selectedDifficulty]);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      const filters: SearchFilters = {
        ...(selectedSubject && { subject: selectedSubject }),
        ...(selectedYear && { year: selectedYear }),
        ...(selectedShift && { shift: selectedShift }),
        ...(selectedTopic && { topic: selectedTopic }),
        ...(selectedDifficulty && { difficulty: selectedDifficulty }),
        ...(searchQuery && { searchQuery }),
      };

      const result = await QuestionsService.getQuestions(filters, 100);
      setQuestions(result.questions);
    } catch (error) {
      console.error('Error loading questions:', error);
      toast.error('Failed to load questions');
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableYears = async () => {
    try {
      const yearsShifts = await QuestionsService.getAvailableYearsAndShifts();
      setAvailableYears(yearsShifts);
    } catch (error) {
      console.error('Error loading years:', error);
    }
  };

  const handleSearch = () => {
    loadQuestions();
  };

  const handleDeleteQuestion = async (questionId: string) => {
    if (!confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
      return;
    }

    try {
      await QuestionsService.deleteQuestion(questionId);
      setQuestions(questions.filter(q => q.id !== questionId));
      toast.success('Question deleted successfully');
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('Failed to delete question');
    }
  };

  const clearFilters = () => {
    setSelectedSubject(null);
    setSelectedYear(null);
    setSelectedShift(null);
    setSelectedTopic(null);
    setSelectedDifficulty(null);
    setSearchQuery('');
  };

  const uniqueYears = [...new Set(availableYears.map(item => item.year))].sort((a, b) => b - a);
  const shiftsForYear = selectedYear 
    ? availableYears.filter(item => item.year === selectedYear).map(item => item.shift).sort()
    : [];

  if (authLoading) {
    return <LoadingPage message="Checking permissions..." />;
  }

  if (!user || !isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You don't have permission to access this page.</p>
          <Link
            href="/"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Manage Questions</h1>
            <p className="text-gray-600">Add, edit, and organize questions in the database</p>
          </div>
          <Link
            href="/admin/questions/new"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Add Question</span>
          </Link>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          {/* Search Bar */}
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={handleSearch}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FunnelIcon className="h-5 w-5" />
              <span>Filters</span>
            </button>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="border-t border-gray-200 pt-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                {/* Subject Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                  <select
                    value={selectedSubject || ''}
                    onChange={(e) => setSelectedSubject(e.target.value || null)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Subjects</option>
                    {subjects.map(subject => (
                      <option key={subject} value={subject}>{subject}</option>
                    ))}
                  </select>
                </div>

                {/* Year Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Year</label>
                  <select
                    value={selectedYear || ''}
                    onChange={(e) => {
                      setSelectedYear(e.target.value ? parseInt(e.target.value) : null);
                      setSelectedShift(null);
                    }}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Years</option>
                    {uniqueYears.map(year => (
                      <option key={year} value={year}>JEE Main {year}</option>
                    ))}
                  </select>
                </div>

                {/* Shift Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Shift</label>
                  <select
                    value={selectedShift || ''}
                    onChange={(e) => setSelectedShift(e.target.value ? parseInt(e.target.value) : null)}
                    disabled={!selectedYear}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                  >
                    <option value="">All Shifts</option>
                    {shiftsForYear.map(shift => (
                      <option key={shift} value={shift}>Shift {shift}</option>
                    ))}
                  </select>
                </div>

                {/* Topic Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Topic</label>
                  <input
                    type="text"
                    placeholder="Enter topic..."
                    value={selectedTopic || ''}
                    onChange={(e) => setSelectedTopic(e.target.value || null)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Difficulty Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                  <select
                    value={selectedDifficulty || ''}
                    onChange={(e) => setSelectedDifficulty(e.target.value || null)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Levels</option>
                    {difficulties.map(difficulty => (
                      <option key={difficulty} value={difficulty}>{difficulty}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="mt-4 flex justify-between items-center">
                <button
                  onClick={clearFilters}
                  className="text-gray-600 hover:text-gray-800 text-sm"
                >
                  Clear all filters
                </button>
                <span className="text-sm text-gray-600">
                  {questions.length} question{questions.length !== 1 ? 's' : ''} found
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Questions List */}
        {loading ? (
          <LoadingList count={5} />
        ) : questions.length > 0 ? (
          <div className="space-y-6">
            {questions.map((question) => (
              <div key={question.id} className="relative">
                <QuestionCard question={question} />
                <div className="absolute top-4 right-4 flex space-x-2">
                  <Link
                    href={`/admin/questions/${question.id}`}
                    className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors"
                    title="Edit question"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </Link>
                  <button
                    onClick={() => handleDeleteQuestion(question.id)}
                    className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors"
                    title="Delete question"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <MagnifyingGlassIcon className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No questions found</h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your search criteria or filters to find more questions.
            </p>
            <Link
              href="/admin/questions/new"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add First Question
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
