'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { QuestionsService } from '@/lib/db/questions';
import { Question } from '@/types';
import { QuestionCard } from '@/components/questions/QuestionCard';
import { LoadingPage, LoadingList } from '@/components/ui/Loading';
import Link from 'next/link';
import { BookmarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

export default function BookmarksPage() {
  const { user, appUser, loading: authLoading } = useAuth();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredQuestions, setFilteredQuestions] = useState<Question[]>([]);

  useEffect(() => {
    if (!authLoading && user && appUser) {
      loadBookmarkedQuestions();
    }
  }, [authLoading, user, appUser]);

  useEffect(() => {
    // Filter questions based on search query
    if (searchQuery.trim()) {
      const filtered = questions.filter(question =>
        question.questionText.toLowerCase().includes(searchQuery.toLowerCase()) ||
        question.solution.toLowerCase().includes(searchQuery.toLowerCase()) ||
        question.topic.toLowerCase().includes(searchQuery.toLowerCase()) ||
        question.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
        question.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredQuestions(filtered);
    } else {
      setFilteredQuestions(questions);
    }
  }, [searchQuery, questions]);

  const loadBookmarkedQuestions = async () => {
    if (!appUser?.bookmarkedQuestions.length) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const bookmarkedQuestions: Question[] = [];
      
      // Load each bookmarked question
      for (const questionId of appUser.bookmarkedQuestions) {
        try {
          const question = await QuestionsService.getQuestion(questionId);
          if (question) {
            bookmarkedQuestions.push(question);
          }
        } catch (error) {
          console.error(`Error loading question ${questionId}:`, error);
        }
      }

      // Sort by subject and question number
      bookmarkedQuestions.sort((a, b) => {
        if (a.subject !== b.subject) {
          return a.subject.localeCompare(b.subject);
        }
        return a.questionNumber - b.questionNumber;
      });

      setQuestions(bookmarkedQuestions);
    } catch (error) {
      console.error('Error loading bookmarked questions:', error);
    } finally {
      setLoading(false);
    }
  };

  if (authLoading) {
    return <LoadingPage message="Loading your bookmarks..." />;
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <BookmarkIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Sign in to view bookmarks</h1>
          <p className="text-gray-600 mb-6">
            Create an account or sign in to bookmark questions and track your progress.
          </p>
          <div className="space-x-4">
            <Link
              href="/auth/signin"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/auth/signup"
              className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Sign Up
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <BookmarkIcon className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">My Bookmarks</h1>
          </div>
          <p className="text-gray-600">
            Questions you've saved for later review
          </p>
        </div>

        {/* Search Bar */}
        {questions.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search your bookmarked questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            {searchQuery && (
              <div className="mt-2 text-sm text-gray-600">
                {filteredQuestions.length} of {questions.length} bookmarks match your search
              </div>
            )}
          </div>
        )}

        {/* Content */}
        {loading ? (
          <LoadingList count={3} />
        ) : questions.length === 0 ? (
          <div className="text-center py-12">
            <BookmarkIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No bookmarks yet</h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Start exploring questions and bookmark the ones you want to review later. 
              Click the bookmark icon on any question to save it here.
            </p>
            <div className="space-x-4">
              <Link
                href="/physics"
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Browse Physics
              </Link>
              <Link
                href="/chemistry"
                className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
              >
                Browse Chemistry
              </Link>
              <Link
                href="/mathematics"
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Browse Mathematics
              </Link>
            </div>
          </div>
        ) : filteredQuestions.length === 0 ? (
          <div className="text-center py-12">
            <MagnifyingGlassIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No matching bookmarks</h3>
            <p className="text-gray-600">
              Try adjusting your search terms to find the questions you're looking for.
            </p>
            <button
              onClick={() => setSearchQuery('')}
              className="mt-4 text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Clear search
            </button>
          </div>
        ) : (
          <>
            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <div className="bg-blue-100 p-3 rounded-full">
                    <BookmarkIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Bookmarks</p>
                    <p className="text-2xl font-bold text-gray-900">{questions.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <div className="bg-green-100 p-3 rounded-full">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Subjects</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {new Set(questions.map(q => q.subject)).size}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <div className="bg-purple-100 p-3 rounded-full">
                    <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Topics</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {new Set(questions.map(q => q.topic)).size}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Subject Breakdown */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Bookmarks by Subject</h3>
              <div className="space-y-3">
                {['Physics', 'Chemistry', 'Mathematics'].map(subject => {
                  const count = questions.filter(q => q.subject === subject).length;
                  const percentage = questions.length > 0 ? (count / questions.length) * 100 : 0;
                  
                  return (
                    <div key={subject} className="flex items-center justify-between">
                      <span className="text-gray-700">{subject}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              subject === 'Physics' ? 'bg-blue-500' :
                              subject === 'Chemistry' ? 'bg-purple-500' : 'bg-green-500'
                            }`}
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium text-gray-900 w-8">{count}</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Questions List */}
            <div className="space-y-6">
              {filteredQuestions.map((question) => (
                <QuestionCard key={question.id} question={question} />
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
