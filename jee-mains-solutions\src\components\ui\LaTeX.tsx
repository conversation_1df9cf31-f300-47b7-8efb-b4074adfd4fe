'use client';

import React from 'react';
import { InlineMath, BlockMath } from 'react-katex';
import 'katex/dist/katex.min.css';

interface LaTeXProps {
  children: string;
  block?: boolean;
  className?: string;
}

export function LaTeX({ children, block = false, className = '' }: LaTeXProps) {
  // Clean up the LaTeX string
  const cleanLatex = children
    .replace(/\\\\/g, '\\\\') // Ensure double backslashes are preserved
    .replace(/\$\$/g, '') // Remove display math delimiters
    .replace(/\$/g, ''); // Remove inline math delimiters

  try {
    if (block) {
      return (
        <div className={`my-4 ${className}`}>
          <BlockMath math={cleanLatex} />
        </div>
      );
    } else {
      return (
        <span className={className}>
          <InlineMath math={cleanLatex} />
        </span>
      );
    }
  } catch (error) {
    console.error('LaTeX rendering error:', error);
    return (
      <span className={`text-red-500 ${className}`}>
        [LaTeX Error: {children}]
      </span>
    );
  }
}

interface MathTextProps {
  children: string;
  className?: string;
}

export function MathText({ children, className = '' }: MathTextProps) {
  // Parse text with inline and block math
  const parts = children.split(/(\$\$[\s\S]*?\$\$|\$[^$]*?\$)/);
  
  return (
    <div className={className}>
      {parts.map((part, index) => {
        if (part.startsWith('$$') && part.endsWith('$$')) {
          // Block math
          const math = part.slice(2, -2);
          return <LaTeX key={index} block>{math}</LaTeX>;
        } else if (part.startsWith('$') && part.endsWith('$')) {
          // Inline math
          const math = part.slice(1, -1);
          return <LaTeX key={index}>{math}</LaTeX>;
        } else {
          // Regular text
          return <span key={index}>{part}</span>;
        }
      })}
    </div>
  );
}

// Common math symbols and expressions
export const MathSymbols = {
  // Greek letters
  alpha: '\\alpha',
  beta: '\\beta',
  gamma: '\\gamma',
  delta: '\\delta',
  epsilon: '\\epsilon',
  theta: '\\theta',
  lambda: '\\lambda',
  mu: '\\mu',
  pi: '\\pi',
  sigma: '\\sigma',
  phi: '\\phi',
  omega: '\\omega',
  
  // Operators
  sum: '\\sum',
  integral: '\\int',
  limit: '\\lim',
  derivative: '\\frac{d}{dx}',
  partial: '\\partial',
  
  // Common expressions
  fraction: (num: string, den: string) => `\\frac{${num}}{${den}}`,
  sqrt: (expr: string) => `\\sqrt{${expr}}`,
  power: (base: string, exp: string) => `${base}^{${exp}}`,
  subscript: (base: string, sub: string) => `${base}_{${sub}}`,
  
  // Chemistry
  arrow: '\\rightarrow',
  equilibrium: '\\rightleftharpoons',
  
  // Physics
  vector: (v: string) => `\\vec{${v}}`,
  magnitude: (v: string) => `|${v}|`,
};

// Utility function to format common mathematical expressions
export function formatMath(text: string): string {
  return text
    // Fractions
    .replace(/(\d+)\/(\d+)/g, '\\frac{$1}{$2}')
    // Superscripts
    .replace(/\^(\d+)/g, '^{$1}')
    // Subscripts
    .replace(/_(\d+)/g, '_{$1}')
    // Square roots
    .replace(/sqrt\(([^)]+)\)/g, '\\sqrt{$1}')
    // Common symbols
    .replace(/\bpi\b/g, '\\pi')
    .replace(/\balpha\b/g, '\\alpha')
    .replace(/\bbeta\b/g, '\\beta')
    .replace(/\bgamma\b/g, '\\gamma')
    .replace(/\bdelta\b/g, '\\delta')
    .replace(/\btheta\b/g, '\\theta')
    .replace(/\blambda\b/g, '\\lambda')
    .replace(/\bmu\b/g, '\\mu')
    .replace(/\bsigma\b/g, '\\sigma')
    .replace(/\bomega\b/g, '\\omega')
    // Infinity
    .replace(/\binfinity\b/g, '\\infty')
    // Degrees
    .replace(/(\d+)\s*degrees?/g, '$1^\\circ');
}
