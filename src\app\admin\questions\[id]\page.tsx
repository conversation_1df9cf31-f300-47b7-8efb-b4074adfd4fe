'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import { QuestionsService } from '@/lib/db/questions';
import { Question } from '@/types';
import { LoadingPage, LoadingButton } from '@/components/ui/Loading';
import { MathText } from '@/components/ui/LaTeX';
import { extractYouTubeVideoId } from '@/lib/utils';
import Link from 'next/link';
import { ArrowLeftIcon, EyeIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const subjects = ['Physics', 'Chemistry', 'Mathematics'];
const difficulties = ['Easy', 'Medium', 'Hard'];

export default function EditQuestionPage() {
  const router = useRouter();
  const params = useParams();
  const questionId = params.id as string;
  const { user, isAdmin, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [question, setQuestion] = useState<Question | null>(null);

  const [formData, setFormData] = useState({
    questionNumber: '',
    subject: 'Physics' as 'Physics' | 'Chemistry' | 'Mathematics',
    year: new Date().getFullYear(),
    shift: 1,
    topic: '',
    difficulty: 'Medium' as 'Easy' | 'Medium' | 'Hard',
    questionText: '',
    options: ['', '', '', ''],
    correctAnswer: '',
    solution: '',
    youtubeUrl: '',
    tags: '',
  });

  useEffect(() => {
    if (!authLoading && isAdmin && questionId) {
      loadQuestion();
    }
  }, [authLoading, isAdmin, questionId]);

  const loadQuestion = async () => {
    try {
      setLoading(true);
      const questionData = await QuestionsService.getQuestion(questionId);
      
      if (!questionData) {
        toast.error('Question not found');
        router.push('/admin/questions');
        return;
      }

      setQuestion(questionData);
      setFormData({
        questionNumber: questionData.questionNumber.toString(),
        subject: questionData.subject,
        year: questionData.year,
        shift: questionData.shift,
        topic: questionData.topic,
        difficulty: questionData.difficulty,
        questionText: questionData.questionText,
        options: questionData.options || ['', '', '', ''],
        correctAnswer: questionData.correctAnswer || '',
        solution: questionData.solution,
        youtubeUrl: questionData.youtubeUrl || '',
        tags: questionData.tags.join(', '),
      });
    } catch (error) {
      console.error('Error loading question:', error);
      toast.error('Failed to load question');
      router.push('/admin/questions');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...formData.options];
    newOptions[index] = value;
    setFormData(prev => ({ ...prev, options: newOptions }));
  };

  const validateForm = () => {
    if (!formData.questionNumber || !formData.questionText || !formData.solution || !formData.topic) {
      toast.error('Please fill in all required fields');
      return false;
    }

    if (formData.youtubeUrl && !extractYouTubeVideoId(formData.youtubeUrl)) {
      toast.error('Please enter a valid YouTube URL');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !question) return;

    try {
      setSaving(true);

      const updates: Partial<Question> = {
        questionNumber: parseInt(formData.questionNumber),
        subject: formData.subject,
        year: formData.year,
        shift: formData.shift,
        topic: formData.topic,
        difficulty: formData.difficulty,
        questionText: formData.questionText,
        options: formData.options.filter(opt => opt.trim() !== ''),
        correctAnswer: formData.correctAnswer || undefined,
        solution: formData.solution,
        youtubeUrl: formData.youtubeUrl || undefined,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag !== ''),
      };

      await QuestionsService.updateQuestion(questionId, updates);
      toast.success('Question updated successfully!');
      router.push('/admin/questions');
    } catch (error) {
      console.error('Error updating question:', error);
      toast.error('Failed to update question');
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return <LoadingPage message="Loading question..." />;
  }

  if (!user || !isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You don't have permission to access this page.</p>
          <Link href="/" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  if (!question) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Question Not Found</h1>
          <p className="text-gray-600 mb-6">The question you're looking for doesn't exist.</p>
          <Link href="/admin/questions" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Back to Questions
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link
              href="/admin/questions"
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5" />
              <span>Back to Questions</span>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Question {question.questionNumber}</h1>
              <p className="text-gray-600">Update question details and solution</p>
            </div>
          </div>
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="flex items-center space-x-2 border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <EyeIcon className="h-5 w-5" />
            <span>{showPreview ? 'Hide Preview' : 'Show Preview'}</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Question Number *
                  </label>
                  <input
                    type="number"
                    value={formData.questionNumber}
                    onChange={(e) => handleInputChange('questionNumber', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <select
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {subjects.map(subject => (
                      <option key={subject} value={subject}>{subject}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Year</label>
                  <input
                    type="number"
                    value={formData.year}
                    onChange={(e) => handleInputChange('year', parseInt(e.target.value))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Shift</label>
                  <input
                    type="number"
                    min="1"
                    max="4"
                    value={formData.shift}
                    onChange={(e) => handleInputChange('shift', parseInt(e.target.value))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                  <select
                    value={formData.difficulty}
                    onChange={(e) => handleInputChange('difficulty', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {difficulties.map(difficulty => (
                      <option key={difficulty} value={difficulty}>{difficulty}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Topic *</label>
                <input
                  type="text"
                  value={formData.topic}
                  onChange={(e) => handleInputChange('topic', e.target.value)}
                  placeholder="e.g., Mechanics, Organic Chemistry, Calculus"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              {/* Question Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Question Text * (LaTeX supported)
                </label>
                <textarea
                  value={formData.questionText}
                  onChange={(e) => handleInputChange('questionText', e.target.value)}
                  rows={4}
                  placeholder="Enter the question text. Use $ for inline math and $$ for display math."
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              {/* Options */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Options (Leave empty if not multiple choice)
                </label>
                <div className="space-y-2">
                  {formData.options.map((option, index) => (
                    <input
                      key={index}
                      type="text"
                      value={option}
                      onChange={(e) => handleOptionChange(index, e.target.value)}
                      placeholder={`Option ${String.fromCharCode(65 + index)}`}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ))}
                </div>
              </div>

              {/* Correct Answer */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Correct Answer</label>
                <input
                  type="text"
                  value={formData.correctAnswer}
                  onChange={(e) => handleInputChange('correctAnswer', e.target.value)}
                  placeholder="Enter the correct answer"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Solution */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Solution * (LaTeX supported)
                </label>
                <textarea
                  value={formData.solution}
                  onChange={(e) => handleInputChange('solution', e.target.value)}
                  rows={8}
                  placeholder="Enter the detailed solution with step-by-step explanation. Use $ for inline math and $$ for display math."
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              {/* YouTube URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">YouTube Video URL</label>
                <input
                  type="url"
                  value={formData.youtubeUrl}
                  onChange={(e) => handleInputChange('youtubeUrl', e.target.value)}
                  placeholder="https://www.youtube.com/watch?v=..."
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) => handleInputChange('tags', e.target.value)}
                  placeholder="kinematics, motion, acceleration"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Link
                  href="/admin/questions"
                  className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </Link>
                <LoadingButton loading={saving} className="bg-blue-600 hover:bg-blue-700">
                  Update Question
                </LoadingButton>
              </div>
            </form>
          </div>

          {/* Preview */}
          {showPreview && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <span className="text-lg font-semibold">Q{formData.questionNumber || '?'}</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">{formData.subject}</span>
                  <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">{formData.difficulty}</span>
                </div>

                <div className="text-sm text-gray-600">
                  JEE Main {formData.year} - Shift {formData.shift} • {formData.topic}
                </div>

                {formData.questionText && (
                  <div className="border-l-4 border-blue-500 pl-4">
                    <MathText>{formData.questionText}</MathText>
                  </div>
                )}

                {formData.options.some(opt => opt.trim()) && (
                  <div className="space-y-2">
                    {formData.options.map((option, index) => 
                      option.trim() && (
                        <div key={index} className="flex items-start space-x-2">
                          <span className="text-sm font-medium text-gray-500 mt-0.5">
                            {String.fromCharCode(65 + index)}.
                          </span>
                          <MathText className="text-sm">{option}</MathText>
                        </div>
                      )
                    )}
                  </div>
                )}

                {formData.correctAnswer && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm font-medium text-green-800 mb-1">Correct Answer:</p>
                    <MathText className="text-green-700">{formData.correctAnswer}</MathText>
                  </div>
                )}

                {formData.solution && (
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Solution:</h4>
                    <MathText>{formData.solution}</MathText>
                  </div>
                )}

                {formData.tags && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.split(',').map((tag, index) => 
                      tag.trim() && (
                        <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                          {tag.trim()}
                        </span>
                      )
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
