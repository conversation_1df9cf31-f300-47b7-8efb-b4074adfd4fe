export interface Question {
  id: string;
  questionNumber: number;
  subject: 'Physics' | 'Chemistry' | 'Mathematics';
  year: number;
  shift: number;
  topic: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  questionText: string;
  options?: string[];
  correctAnswer?: string;
  solution: string;
  youtubeUrl?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  viewCount: number;
  bookmarkCount: number;
}

export interface User {
  id: string;
  email: string;
  displayName: string;
  role: 'admin' | 'user';
  bookmarkedQuestions: string[];
  solvedQuestions: string[];
  createdAt: Date;
  lastLoginAt: Date;
}

export interface UserProgress {
  userId: string;
  questionId: string;
  status: 'solved' | 'attempted' | 'bookmarked';
  timeSpent?: number;
  attempts: number;
  lastAttemptAt: Date;
}

export interface Analytics {
  id: string;
  questionId: string;
  userId?: string;
  action: 'view' | 'bookmark' | 'solve' | 'video_watch';
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface Subject {
  name: 'Physics' | 'Chemistry' | 'Mathematics';
  icon: string;
  color: string;
  topics: string[];
}

export interface YearShift {
  year: number;
  shift: number;
  questionCount: number;
}

export interface SearchFilters {
  subject?: string;
  year?: number;
  shift?: number;
  topic?: string;
  difficulty?: string;
  tags?: string[];
  searchQuery?: string;
}

export interface QuestionStats {
  totalQuestions: number;
  solvedQuestions: number;
  bookmarkedQuestions: number;
  averageTime: number;
}
