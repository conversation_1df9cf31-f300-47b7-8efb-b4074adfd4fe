'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}

const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_TRACKING_ID;

export function GoogleAnalytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (!GA_TRACKING_ID) return;

    // Load Google Analytics script
    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;
    document.head.appendChild(script1);

    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${GA_TRACKING_ID}', {
        page_title: document.title,
        page_location: window.location.href,
      });
    `;
    document.head.appendChild(script2);

    return () => {
      document.head.removeChild(script1);
      document.head.removeChild(script2);
    };
  }, []);

  useEffect(() => {
    if (!GA_TRACKING_ID || !window.gtag) return;

    const url = pathname + searchParams.toString();
    
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
      page_title: document.title,
    });
  }, [pathname, searchParams]);

  return null;
}

// Helper functions for tracking events
export const trackEvent = (action: string, category: string, label?: string, value?: number) => {
  if (!GA_TRACKING_ID || !window.gtag) return;

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  });
};

export const trackQuestionView = (questionId: string, subject: string) => {
  trackEvent('view_question', 'engagement', `${subject}_${questionId}`);
};

export const trackVideoPlay = (questionId: string, subject: string) => {
  trackEvent('play_video', 'engagement', `${subject}_${questionId}`);
};

export const trackBookmark = (questionId: string, subject: string) => {
  trackEvent('bookmark_question', 'engagement', `${subject}_${questionId}`);
};

export const trackSearch = (query: string, subject?: string) => {
  trackEvent('search', 'engagement', `${subject || 'all'}_${query}`);
};

export const trackSignUp = (method: string = 'email') => {
  trackEvent('sign_up', 'user', method);
};

export const trackSignIn = (method: string = 'email') => {
  trackEvent('sign_in', 'user', method);
};
