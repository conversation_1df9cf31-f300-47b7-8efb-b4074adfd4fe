var za=Object.defineProperty;var Us=e=>{throw TypeError(e)};var xr=(e,t)=>{for(var r in t)za(e,r,{get:t[r],enumerable:!0})};var Xs=(e,t,r)=>t.has(e)||Us("Cannot "+r);var mt=(e,t,r)=>(Xs(e,t,"read from private field"),r?r.call(e):t.get(e)),Ys=(e,t,r)=>t.has(e)?Us("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Hs=(e,t,r,n)=>(Xs(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);var Rs={};xr(Rs,{languages:()=>Tm,options:()=>Ha,printers:()=>dm});var Vs=[{name:"JavaScript",type:"programming",extensions:[".js","._js",".bones",".cjs",".es",".es6",".gs",".jake",".javascript",".jsb",".jscad",".jsfl",".jslib",".jsm",".jspre",".jss",".mjs",".njs",".pac",".sjs",".ssjs",".xsjs",".xsjslib",".start.frag",".end.frag",".wxs"],tmScope:"source.js",aceMode:"javascript",aliases:["js","node"],codemirrorMode:"javascript",codemirrorMimeType:"text/javascript",interpreters:["chakra","d8","gjs","js","node","nodejs","qjs","rhino","v8","v8-shell","zx"],filenames:["Jakefile","start.frag","end.frag"],parsers:["babel","acorn","espree","meriyah","babel-flow","babel-ts","flow","typescript"],vscodeLanguageIds:["javascript","mongo"],linguistLanguageId:183},{name:"Flow",type:"programming",extensions:[".js.flow"],tmScope:"source.js",aceMode:"javascript",aliases:[],codemirrorMode:"javascript",codemirrorMimeType:"text/javascript",interpreters:["chakra","d8","gjs","js","node","nodejs","qjs","rhino","v8","v8-shell"],filenames:[],parsers:["flow","babel-flow"],vscodeLanguageIds:["javascript"],linguistLanguageId:183},{name:"JSX",type:"programming",extensions:[".jsx"],tmScope:"source.js.jsx",aceMode:"javascript",aliases:void 0,codemirrorMode:"jsx",codemirrorMimeType:"text/jsx",interpreters:void 0,filenames:void 0,parsers:["babel","babel-flow","babel-ts","flow","typescript","espree","meriyah"],vscodeLanguageIds:["javascriptreact"],group:"JavaScript",linguistLanguageId:183},{name:"TypeScript",type:"programming",extensions:[".ts",".cts",".mts"],tmScope:"source.ts",aceMode:"typescript",aliases:["ts"],codemirrorMode:"javascript",codemirrorMimeType:"application/typescript",interpreters:["bun","deno","ts-node","tsx"],parsers:["typescript","babel-ts"],vscodeLanguageIds:["typescript"],linguistLanguageId:378},{name:"TSX",type:"programming",extensions:[".tsx"],tmScope:"source.tsx",aceMode:"javascript",codemirrorMode:"jsx",codemirrorMimeType:"text/jsx",group:"TypeScript",parsers:["typescript","babel-ts"],vscodeLanguageIds:["typescriptreact"],linguistLanguageId:94901924}];var vs={};xr(vs,{canAttachComment:()=>kp,embed:()=>oi,experimentalFeatures:()=>fm,getCommentChildNodes:()=>Ip,getVisitorKeys:()=>br,handleComments:()=>ts,insertPragma:()=>di,isBlockComment:()=>te,isGap:()=>Lp,massageAstNode:()=>Bu,print:()=>Ga,printComment:()=>ju,willPrintOwnComments:()=>rs});var Za=(e,t,r,n)=>{if(!(e&&t==null))return t.replaceAll?t.replaceAll(r,n):r.global?t.replace(r,n):t.split(r).join(n)},X=Za;var eo=(e,t,r)=>{if(!(e&&t==null))return Array.isArray(t)||typeof t=="string"?t[r<0?t.length+r:r]:t.at(r)},v=eo;function to(e){return e!==null&&typeof e=="object"}var $s=to;function*ro(e,t){let{getVisitorKeys:r,filter:n=()=>!0}=t,s=u=>$s(u)&&n(u);for(let u of r(e)){let i=e[u];if(Array.isArray(i))for(let a of i)s(a)&&(yield a);else s(i)&&(yield i)}}function*no(e,t){let r=[e];for(let n=0;n<r.length;n++){let s=r[n];for(let u of ro(s,t))yield u,r.push(u)}}function Ks(e,{getVisitorKeys:t,predicate:r}){for(let n of no(e,{getVisitorKeys:t}))if(r(n))return!0;return!1}var Qs=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;function zs(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function Zs(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}var eu=e=>!(zs(e)||Zs(e));var so=/[^\x20-\x7F]/u;function uo(e){if(!e)return 0;if(!so.test(e))return e.length;e=e.replace(Qs(),"  ");let t=0;for(let r of e){let n=r.codePointAt(0);n<=31||n>=127&&n<=159||n>=768&&n<=879||(t+=eu(n)?1:2)}return t}var st=uo;function hr(e){return(t,r,n)=>{let s=!!(n!=null&&n.backwards);if(r===!1)return!1;let{length:u}=t,i=r;for(;i>=0&&i<u;){let a=t.charAt(i);if(e instanceof RegExp){if(!e.test(a))return i}else if(!e.includes(a))return i;s?i--:i++}return i===-1||i===u?i:!1}}var Jm=hr(/\s/u),Ye=hr(" 	"),tu=hr(",; 	"),ru=hr(/[^\n\r]/u);function io(e,t,r){let n=!!(r!=null&&r.backwards);if(t===!1)return!1;let s=e.charAt(t);if(n){if(e.charAt(t-1)==="\r"&&s===`
`)return t-2;if(s===`
`||s==="\r"||s==="\u2028"||s==="\u2029")return t-1}else{if(s==="\r"&&e.charAt(t+1)===`
`)return t+2;if(s===`
`||s==="\r"||s==="\u2028"||s==="\u2029")return t+1}return t}var He=io;function ao(e,t,r={}){let n=Ye(e,r.backwards?t-1:t,r),s=He(e,n,r);return n!==s}var ee=ao;function oo(e,t){if(t===!1)return!1;if(e.charAt(t)==="/"&&e.charAt(t+1)==="*"){for(let r=t+2;r<e.length;++r)if(e.charAt(r)==="*"&&e.charAt(r+1)==="/")return r+2}return t}var _t=oo;function po(e,t){return t===!1?!1:e.charAt(t)==="/"&&e.charAt(t+1)==="/"?ru(e,t):t}var Mt=po;function co(e,t){let r=null,n=t;for(;n!==r;)r=n,n=tu(e,n),n=_t(e,n),n=Ye(e,n);return n=Mt(e,n),n=He(e,n),n!==!1&&ee(e,n)}var vt=co;function lo(e){return Array.isArray(e)&&e.length>0}var O=lo;var nu=new Proxy(()=>{},{get:()=>nu}),jt=nu;var gr="'",su='"';function mo(e,t){let r=t===!0||t===gr?gr:su,n=r===gr?su:gr,s=0,u=0;for(let i of e)i===r?s++:i===n&&u++;return s>u?n:r}var Sr=mo;function yo(e,t,r){let n=t==='"'?"'":'"',u=X(!1,e,/\\(.)|(["'])/gsu,(i,a,o)=>a===n?a:o===t?"\\"+o:o||(r&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/u.test(a)?a:"\\"+a));return t+u+t}var uu=yo;function fo(e,t){jt.ok(/^(?<quote>["']).*\k<quote>$/su.test(e));let r=e.slice(1,-1),n=t.parser==="json"||t.parser==="jsonc"||t.parser==="json5"&&t.quoteProps==="preserve"&&!t.singleQuote?'"':t.__isInHtmlAttribute?"'":Sr(r,t.singleQuote);return e.charAt(0)===n?e:uu(r,n,!1)}var ut=fo;var iu=e=>Number.isInteger(e)&&e>=0;function j(e){var n,s,u;let t=((n=e.range)==null?void 0:n[0])??e.start,r=(u=((s=e.declaration)==null?void 0:s.decorators)??e.decorators)==null?void 0:u[0];return r?Math.min(j(r),t):t}function P(e){var r;return((r=e.range)==null?void 0:r[1])??e.end}function Bt(e,t){let r=j(e);return iu(r)&&r===j(t)}function Do(e,t){let r=P(e);return iu(r)&&r===P(t)}function au(e,t){return Bt(e,t)&&Do(e,t)}var rr=null;function nr(e){if(rr!==null&&typeof rr.property){let t=rr;return rr=nr.prototype=null,t}return rr=nr.prototype=e??Object.create(null),new nr}var Eo=10;for(let e=0;e<=Eo;e++)nr();function On(e){return nr(e)}function Fo(e,t="type"){On(e);function r(n){let s=n[t],u=e[s];if(!Array.isArray(u))throw Object.assign(new Error(`Missing visitor keys for '${s}'.`),{node:n});return u}return r}var Br=Fo;var ou={ArrayExpression:["elements"],AssignmentExpression:["left","right"],BinaryExpression:["left","right"],InterpreterDirective:[],Directive:["value"],DirectiveLiteral:[],BlockStatement:["directives","body"],BreakStatement:["label"],CallExpression:["callee","typeParameters","typeArguments","arguments"],CatchClause:["param","body"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DoWhileStatement:["body","test"],EmptyStatement:[],ExpressionStatement:["expression"],File:["program"],ForInStatement:["left","right","body"],ForStatement:["init","test","update","body"],FunctionDeclaration:["id","typeParameters","params","predicate","returnType","body"],FunctionExpression:["id","typeParameters","params","returnType","body"],Identifier:["typeAnnotation","decorators"],IfStatement:["test","consequent","alternate"],LabeledStatement:["label","body"],StringLiteral:[],NumericLiteral:[],NullLiteral:[],BooleanLiteral:[],RegExpLiteral:[],LogicalExpression:["left","right"],MemberExpression:["object","property"],NewExpression:["callee","typeParameters","typeArguments","arguments"],Program:["directives","body"],ObjectExpression:["properties"],ObjectMethod:["decorators","key","typeParameters","params","returnType","body"],ObjectProperty:["decorators","key","value"],RestElement:["argument","typeAnnotation","decorators"],ReturnStatement:["argument"],SequenceExpression:["expressions"],ParenthesizedExpression:["expression"],SwitchCase:["test","consequent"],SwitchStatement:["discriminant","cases"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],AssignmentPattern:["left","right","decorators","typeAnnotation"],ArrayPattern:["elements","typeAnnotation","decorators"],ArrowFunctionExpression:["typeParameters","params","predicate","returnType","body"],ClassBody:["body"],ClassExpression:["decorators","id","typeParameters","superClass","superTypeParameters","mixins","implements","body","superTypeArguments"],ClassDeclaration:["decorators","id","typeParameters","superClass","superTypeParameters","mixins","implements","body","superTypeArguments"],ExportAllDeclaration:["source","attributes","exported"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["declaration","specifiers","source","attributes"],ExportSpecifier:["local","exported"],ForOfStatement:["left","right","body"],ImportDeclaration:["specifiers","source","attributes"],ImportDefaultSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["imported","local"],ImportExpression:["source","options"],MetaProperty:["meta","property"],ClassMethod:["decorators","key","typeParameters","params","returnType","body"],ObjectPattern:["decorators","properties","typeAnnotation"],SpreadElement:["argument"],Super:[],TaggedTemplateExpression:["tag","typeParameters","quasi","typeArguments"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],YieldExpression:["argument"],AwaitExpression:["argument"],BigIntLiteral:[],ExportNamespaceSpecifier:["exported"],OptionalMemberExpression:["object","property"],OptionalCallExpression:["callee","typeParameters","typeArguments","arguments"],ClassProperty:["decorators","variance","key","typeAnnotation","value"],ClassAccessorProperty:["decorators","key","typeAnnotation","value"],ClassPrivateProperty:["decorators","variance","key","typeAnnotation","value"],ClassPrivateMethod:["decorators","key","typeParameters","params","returnType","body"],PrivateName:["id"],StaticBlock:["body"],ImportAttribute:["key","value"],AnyTypeAnnotation:[],ArrayTypeAnnotation:["elementType"],BooleanTypeAnnotation:[],BooleanLiteralTypeAnnotation:[],NullLiteralTypeAnnotation:[],ClassImplements:["id","typeParameters"],DeclareClass:["id","typeParameters","extends","mixins","implements","body"],DeclareFunction:["id","predicate"],DeclareInterface:["id","typeParameters","extends","body"],DeclareModule:["id","body"],DeclareModuleExports:["typeAnnotation"],DeclareTypeAlias:["id","typeParameters","right"],DeclareOpaqueType:["id","typeParameters","supertype"],DeclareVariable:["id"],DeclareExportDeclaration:["declaration","specifiers","source","attributes"],DeclareExportAllDeclaration:["source","attributes"],DeclaredPredicate:["value"],ExistsTypeAnnotation:[],FunctionTypeAnnotation:["typeParameters","this","params","rest","returnType"],FunctionTypeParam:["name","typeAnnotation"],GenericTypeAnnotation:["id","typeParameters"],InferredPredicate:[],InterfaceExtends:["id","typeParameters"],InterfaceDeclaration:["id","typeParameters","extends","body"],InterfaceTypeAnnotation:["extends","body"],IntersectionTypeAnnotation:["types"],MixedTypeAnnotation:[],EmptyTypeAnnotation:[],NullableTypeAnnotation:["typeAnnotation"],NumberLiteralTypeAnnotation:[],NumberTypeAnnotation:[],ObjectTypeAnnotation:["properties","indexers","callProperties","internalSlots"],ObjectTypeInternalSlot:["id","value"],ObjectTypeCallProperty:["value"],ObjectTypeIndexer:["variance","id","key","value"],ObjectTypeProperty:["key","value","variance"],ObjectTypeSpreadProperty:["argument"],OpaqueType:["id","typeParameters","supertype","impltype"],QualifiedTypeIdentifier:["qualification","id"],StringLiteralTypeAnnotation:[],StringTypeAnnotation:[],SymbolTypeAnnotation:[],ThisTypeAnnotation:[],TupleTypeAnnotation:["types","elementTypes"],TypeofTypeAnnotation:["argument","typeArguments"],TypeAlias:["id","typeParameters","right"],TypeAnnotation:["typeAnnotation"],TypeCastExpression:["expression","typeAnnotation"],TypeParameter:["bound","default","variance"],TypeParameterDeclaration:["params"],TypeParameterInstantiation:["params"],UnionTypeAnnotation:["types"],Variance:[],VoidTypeAnnotation:[],EnumDeclaration:["id","body"],EnumBooleanBody:["members"],EnumNumberBody:["members"],EnumStringBody:["members"],EnumSymbolBody:["members"],EnumBooleanMember:["id","init"],EnumNumberMember:["id","init"],EnumStringMember:["id","init"],EnumDefaultedMember:["id"],IndexedAccessType:["objectType","indexType"],OptionalIndexedAccessType:["objectType","indexType"],JSXAttribute:["name","value"],JSXClosingElement:["name"],JSXElement:["openingElement","children","closingElement"],JSXEmptyExpression:[],JSXExpressionContainer:["expression"],JSXSpreadChild:["expression"],JSXIdentifier:[],JSXMemberExpression:["object","property"],JSXNamespacedName:["namespace","name"],JSXOpeningElement:["name","typeParameters","typeArguments","attributes"],JSXSpreadAttribute:["argument"],JSXText:[],JSXFragment:["openingFragment","children","closingFragment"],JSXOpeningFragment:[],JSXClosingFragment:[],Noop:[],Placeholder:[],V8IntrinsicIdentifier:[],ArgumentPlaceholder:[],BindExpression:["object","callee"],Decorator:["expression"],DoExpression:["body"],ExportDefaultSpecifier:["exported"],ModuleExpression:["body"],TopicReference:[],PipelineTopicExpression:["expression"],PipelineBareFunction:["callee"],PipelinePrimaryTopicReference:[],TSParameterProperty:["parameter","decorators"],TSDeclareFunction:["id","typeParameters","params","returnType","body"],TSDeclareMethod:["decorators","key","typeParameters","params","returnType"],TSQualifiedName:["left","right"],TSCallSignatureDeclaration:["typeParameters","parameters","typeAnnotation","params","returnType"],TSConstructSignatureDeclaration:["typeParameters","parameters","typeAnnotation","params","returnType"],TSPropertySignature:["key","typeAnnotation"],TSMethodSignature:["key","typeParameters","parameters","typeAnnotation","params","returnType"],TSIndexSignature:["parameters","typeAnnotation"],TSAnyKeyword:[],TSBooleanKeyword:[],TSBigIntKeyword:[],TSIntrinsicKeyword:[],TSNeverKeyword:[],TSNullKeyword:[],TSNumberKeyword:[],TSObjectKeyword:[],TSStringKeyword:[],TSSymbolKeyword:[],TSUndefinedKeyword:[],TSUnknownKeyword:[],TSVoidKeyword:[],TSThisType:[],TSFunctionType:["typeParameters","parameters","typeAnnotation","params","returnType"],TSConstructorType:["typeParameters","parameters","typeAnnotation","params","returnType"],TSTypeReference:["typeName","typeParameters","typeArguments"],TSTypePredicate:["parameterName","typeAnnotation"],TSTypeQuery:["exprName","typeParameters","typeArguments"],TSTypeLiteral:["members"],TSArrayType:["elementType"],TSTupleType:["elementTypes"],TSOptionalType:["typeAnnotation"],TSRestType:["typeAnnotation"],TSNamedTupleMember:["label","elementType"],TSUnionType:["types"],TSIntersectionType:["types"],TSConditionalType:["checkType","extendsType","trueType","falseType"],TSInferType:["typeParameter"],TSParenthesizedType:["typeAnnotation"],TSTypeOperator:["typeAnnotation"],TSIndexedAccessType:["objectType","indexType"],TSMappedType:["nameType","typeAnnotation","key","constraint"],TSTemplateLiteralType:["quasis","types"],TSLiteralType:["literal"],TSExpressionWithTypeArguments:["expression","typeParameters"],TSInterfaceDeclaration:["id","typeParameters","extends","body"],TSInterfaceBody:["body"],TSTypeAliasDeclaration:["id","typeParameters","typeAnnotation"],TSInstantiationExpression:["expression","typeParameters","typeArguments"],TSAsExpression:["expression","typeAnnotation"],TSSatisfiesExpression:["expression","typeAnnotation"],TSTypeAssertion:["typeAnnotation","expression"],TSEnumBody:["members"],TSEnumDeclaration:["id","body"],TSEnumMember:["id","initializer"],TSModuleDeclaration:["id","body"],TSModuleBlock:["body"],TSImportType:["argument","options","qualifier","typeParameters","typeArguments"],TSImportEqualsDeclaration:["id","moduleReference"],TSExternalModuleReference:["expression"],TSNonNullExpression:["expression"],TSExportAssignment:["expression"],TSNamespaceExportDeclaration:["id"],TSTypeAnnotation:["typeAnnotation"],TSTypeParameterInstantiation:["params"],TSTypeParameterDeclaration:["params"],TSTypeParameter:["constraint","default","name"],ChainExpression:["expression"],ExperimentalRestProperty:["argument"],ExperimentalSpreadProperty:["argument"],Literal:[],MethodDefinition:["decorators","key","value"],PrivateIdentifier:[],Property:["key","value"],PropertyDefinition:["decorators","key","typeAnnotation","value","variance"],AccessorProperty:["decorators","key","typeAnnotation","value"],TSAbstractAccessorProperty:["decorators","key","typeAnnotation"],TSAbstractKeyword:[],TSAbstractMethodDefinition:["key","value"],TSAbstractPropertyDefinition:["decorators","key","typeAnnotation"],TSAsyncKeyword:[],TSClassImplements:["expression","typeArguments","typeParameters"],TSDeclareKeyword:[],TSEmptyBodyFunctionExpression:["id","typeParameters","params","returnType"],TSExportKeyword:[],TSInterfaceHeritage:["expression","typeArguments","typeParameters"],TSPrivateKeyword:[],TSProtectedKeyword:[],TSPublicKeyword:[],TSReadonlyKeyword:[],TSStaticKeyword:[],AsConstExpression:["expression"],AsExpression:["expression","typeAnnotation"],BigIntLiteralTypeAnnotation:[],BigIntTypeAnnotation:[],ComponentDeclaration:["id","params","body","typeParameters","rendersType"],ComponentParameter:["name","local"],ComponentTypeAnnotation:["params","rest","typeParameters","rendersType"],ComponentTypeParameter:["name","typeAnnotation"],ConditionalTypeAnnotation:["checkType","extendsType","trueType","falseType"],DeclareComponent:["id","params","rest","typeParameters","rendersType"],DeclareEnum:["id","body"],DeclareHook:["id"],DeclareNamespace:["id","body"],EnumBigIntBody:["members"],EnumBigIntMember:["id","init"],HookDeclaration:["id","params","body","typeParameters","returnType"],HookTypeAnnotation:["params","returnType","rest","typeParameters"],InferTypeAnnotation:["typeParameter"],KeyofTypeAnnotation:["argument"],ObjectTypeMappedTypeProperty:["keyTparam","propType","sourceType","variance"],QualifiedTypeofIdentifier:["qualification","id"],TupleTypeLabeledElement:["label","elementType","variance"],TupleTypeSpreadElement:["label","typeAnnotation"],TypeOperator:["typeAnnotation"],TypePredicate:["parameterName","typeAnnotation","asserts"],NGChainedExpression:["expressions"],NGEmptyExpression:[],NGPipeExpression:["left","right","arguments"],NGMicrosyntax:["body"],NGMicrosyntaxAs:["key","alias"],NGMicrosyntaxExpression:["expression","alias"],NGMicrosyntaxKey:[],NGMicrosyntaxKeyedExpression:["key","expression"],NGMicrosyntaxLet:["key","value"],NGRoot:["node"],JsExpressionRoot:["node"],JsonRoot:["node"],TSJSDocAllType:[],TSJSDocUnknownType:[],TSJSDocNullableType:["typeAnnotation"],TSJSDocNonNullableType:["typeAnnotation"],NeverTypeAnnotation:[],SatisfiesExpression:["expression","typeAnnotation"],UndefinedTypeAnnotation:[],UnknownTypeAnnotation:[]};var Co=Br(ou),br=Co;function Ao(e){let t=new Set(e);return r=>t.has(r==null?void 0:r.type)}var R=Ao;function To(e){var t;return((t=e.extra)==null?void 0:t.raw)??e.raw}var ae=To;var xo=R(["Block","CommentBlock","MultiLine"]),te=xo;var ho=R(["AnyTypeAnnotation","ThisTypeAnnotation","NumberTypeAnnotation","VoidTypeAnnotation","BooleanTypeAnnotation","BigIntTypeAnnotation","SymbolTypeAnnotation","StringTypeAnnotation","NeverTypeAnnotation","UndefinedTypeAnnotation","UnknownTypeAnnotation","EmptyTypeAnnotation","MixedTypeAnnotation"]),Pr=ho;var go=R(["Line","CommentLine","SingleLine","HashbangComment","HTMLOpen","HTMLClose","Hashbang","InterpreterDirective"]),At=go;function So(e,t){let r=t.split(".");for(let n=r.length-1;n>=0;n--){let s=r[n];if(n===0)return e.type==="Identifier"&&e.name===s;if(e.type!=="MemberExpression"||e.optional||e.computed||e.property.type!=="Identifier"||e.property.name!==s)return!1;e=e.object}}function Bo(e,t){return t.some(r=>So(e,r))}var pu=Bo;function bo({type:e}){return e.startsWith("TS")&&e.endsWith("Keyword")}var kr=bo;function ur(e,t){return t(e)||Ks(e,{getVisitorKeys:br,predicate:t})}function Jt(e){return e.type==="AssignmentExpression"||e.type==="BinaryExpression"||e.type==="LogicalExpression"||e.type==="NGPipeExpression"||e.type==="ConditionalExpression"||w(e)||G(e)||e.type==="SequenceExpression"||e.type==="TaggedTemplateExpression"||e.type==="BindExpression"||e.type==="UpdateExpression"&&!e.prefix||xe(e)||e.type==="TSNonNullExpression"||e.type==="ChainExpression"}function mu(e){return e.expressions?e.expressions[0]:e.left??e.test??e.callee??e.object??e.tag??e.argument??e.expression}function Lr(e){if(e.expressions)return["expressions",0];if(e.left)return["left"];if(e.test)return["test"];if(e.object)return["object"];if(e.callee)return["callee"];if(e.tag)return["tag"];if(e.argument)return["argument"];if(e.expression)return["expression"];throw new Error("Unexpected node has no left side.")}var yu=R(["ExportDefaultDeclaration","DeclareExportDeclaration","ExportNamedDeclaration","ExportAllDeclaration","DeclareExportAllDeclaration"]),U=R(["ArrayExpression"]),ue=R(["ObjectExpression"]);function fu(e){return e.type==="LogicalExpression"&&e.operator==="??"}function ye(e){return e.type==="NumericLiteral"||e.type==="Literal"&&typeof e.value=="number"}function Du(e){return e.type==="BooleanLiteral"||e.type==="Literal"&&typeof e.value=="boolean"}function Rn(e){return e.type==="UnaryExpression"&&(e.operator==="+"||e.operator==="-")&&ye(e.argument)}function K(e){return!!(e&&(e.type==="StringLiteral"||e.type==="Literal"&&typeof e.value=="string"))}function Jn(e){return e.type==="RegExpLiteral"||e.type==="Literal"&&!!e.regex}var wr=R(["Literal","BooleanLiteral","BigIntLiteral","DirectiveLiteral","NullLiteral","NumericLiteral","RegExpLiteral","StringLiteral"]),Po=R(["Identifier","ThisExpression","Super","PrivateName","PrivateIdentifier"]),Re=R(["ObjectTypeAnnotation","TSTypeLiteral","TSMappedType"]),Rt=R(["FunctionExpression","ArrowFunctionExpression"]);function ko(e){return e.type==="FunctionExpression"||e.type==="ArrowFunctionExpression"&&e.body.type==="BlockStatement"}function _n(e){return w(e)&&e.callee.type==="Identifier"&&["async","inject","fakeAsync","waitForAsync"].includes(e.callee.name)}var Y=R(["JSXElement","JSXFragment"]);function bt(e){return e.method&&e.kind==="init"||e.kind==="get"||e.kind==="set"}function Or(e){return(e.type==="ObjectTypeProperty"||e.type==="ObjectTypeInternalSlot")&&!e.static&&!e.method&&e.kind!=="get"&&e.kind!=="set"&&e.value.type==="FunctionTypeAnnotation"}function Eu(e){return(e.type==="TypeAnnotation"||e.type==="TSTypeAnnotation")&&e.typeAnnotation.type==="FunctionTypeAnnotation"&&!e.static&&!Bt(e,e.typeAnnotation)}var Fe=R(["BinaryExpression","LogicalExpression","NGPipeExpression"]);function dt(e){return G(e)||e.type==="BindExpression"&&!!e.object}var Io=R(["TSThisType","NullLiteralTypeAnnotation","BooleanLiteralTypeAnnotation","StringLiteralTypeAnnotation","BigIntLiteralTypeAnnotation","NumberLiteralTypeAnnotation","TSLiteralType","TSTemplateLiteralType"]);function Nt(e){return kr(e)||Pr(e)||Io(e)||(e.type==="GenericTypeAnnotation"||e.type==="TSTypeReference")&&!e.typeParameters&&!e.typeArguments}function Lo(e){return e.type==="Identifier"&&(e.name==="beforeEach"||e.name==="beforeAll"||e.name==="afterEach"||e.name==="afterAll")}var wo=["it","it.only","it.skip","describe","describe.only","describe.skip","test","test.only","test.skip","test.step","test.describe","test.describe.only","test.describe.parallel","test.describe.parallel.only","test.describe.serial","test.describe.serial.only","skip","xit","xdescribe","xtest","fit","fdescribe","ftest"];function Oo(e){return pu(e,wo)}function Pt(e,t){if((e==null?void 0:e.type)!=="CallExpression"||e.optional)return!1;let r=le(e);if(r.length===1){if(_n(e)&&Pt(t))return Rt(r[0]);if(Lo(e.callee))return _n(r[0])}else if((r.length===2||r.length===3)&&(r[0].type==="TemplateLiteral"||K(r[0]))&&Oo(e.callee))return r[2]&&!ye(r[2])?!1:(r.length===2?Rt(r[1]):ko(r[1])&&Q(r[1]).length<=1)||_n(r[1]);return!1}var Fu=e=>t=>((t==null?void 0:t.type)==="ChainExpression"&&(t=t.expression),e(t)),w=Fu(R(["CallExpression","OptionalCallExpression"])),G=Fu(R(["MemberExpression","OptionalMemberExpression"]));function Nn(e,t=5){return Cu(e,t)<=t}function Cu(e,t){let r=0;for(let n in e){let s=e[n];if(s&&typeof s=="object"&&typeof s.type=="string"&&(r++,r+=Cu(s,t-r)),r>t)return r}return r}var _o=.25;function ir(e,t){let{printWidth:r}=t;if(d(e))return!1;let n=r*_o;if(e.type==="ThisExpression"||e.type==="Identifier"&&e.name.length<=n||Rn(e)&&!d(e.argument))return!0;let s=e.type==="Literal"&&"regex"in e&&e.regex.pattern||e.type==="RegExpLiteral"&&e.pattern;return s?s.length<=n:K(e)?ut(ae(e),t).length<=n:e.type==="TemplateLiteral"?e.expressions.length===0&&e.quasis[0].value.raw.length<=n&&!e.quasis[0].value.raw.includes(`
`):e.type==="UnaryExpression"?ir(e.argument,{printWidth:r}):e.type==="CallExpression"&&e.arguments.length===0&&e.callee.type==="Identifier"?e.callee.name.length<=n-2:wr(e)}function de(e,t){return Y(t)?kt(t):d(t,h.Leading,r=>ee(e,P(r)))}function cu(e){return e.quasis.some(t=>t.value.raw.includes(`
`))}function _r(e,t){return(e.type==="TemplateLiteral"&&cu(e)||e.type==="TaggedTemplateExpression"&&cu(e.quasi))&&!ee(t,j(e),{backwards:!0})}function Mr(e){if(!d(e))return!1;let t=v(!1,Ve(e,h.Dangling),-1);return t&&!te(t)}function Au(e){if(e.length<=1)return!1;let t=0;for(let r of e)if(Rt(r)){if(t+=1,t>1)return!0}else if(w(r)){for(let n of le(r))if(Rt(n))return!0}return!1}function vr(e){let{node:t,parent:r,key:n}=e;return n==="callee"&&w(t)&&w(r)&&r.arguments.length>0&&t.arguments.length>r.arguments.length}var Mo=new Set(["!","-","+","~"]);function we(e,t=2){if(t<=0)return!1;if(e.type==="ChainExpression"||e.type==="TSNonNullExpression")return we(e.expression,t);let r=n=>we(n,t-1);if(Jn(e))return st(e.pattern??e.regex.pattern)<=5;if(wr(e)||Po(e)||e.type==="ArgumentPlaceholder")return!0;if(e.type==="TemplateLiteral")return e.quasis.every(n=>!n.value.raw.includes(`
`))&&e.expressions.every(r);if(ue(e))return e.properties.every(n=>!n.computed&&(n.shorthand||n.value&&r(n.value)));if(U(e))return e.elements.every(n=>n===null||r(n));if(yt(e)){if(e.type==="ImportExpression"||we(e.callee,t)){let n=le(e);return n.length<=t&&n.every(r)}return!1}return G(e)?we(e.object,t)&&we(e.property,t):e.type==="UnaryExpression"&&Mo.has(e.operator)||e.type==="UpdateExpression"?we(e.argument,t):!1}function du(e){return e}function ce(e,t="es5"){return e.trailingComma==="es5"&&t==="es5"||e.trailingComma==="all"&&(t==="all"||t==="es5")}function pe(e,t){switch(e.type){case"BinaryExpression":case"LogicalExpression":case"AssignmentExpression":case"NGPipeExpression":return pe(e.left,t);case"MemberExpression":case"OptionalMemberExpression":return pe(e.object,t);case"TaggedTemplateExpression":return e.tag.type==="FunctionExpression"?!1:pe(e.tag,t);case"CallExpression":case"OptionalCallExpression":return e.callee.type==="FunctionExpression"?!1:pe(e.callee,t);case"ConditionalExpression":return pe(e.test,t);case"UpdateExpression":return!e.prefix&&pe(e.argument,t);case"BindExpression":return e.object&&pe(e.object,t);case"SequenceExpression":return pe(e.expressions[0],t);case"ChainExpression":case"TSSatisfiesExpression":case"TSAsExpression":case"TSNonNullExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":return pe(e.expression,t);default:return t(e)}}var lu={"==":!0,"!=":!0,"===":!0,"!==":!0},Ir={"*":!0,"/":!0,"%":!0},jn={">>":!0,">>>":!0,"<<":!0};function ar(e,t){return!(sr(t)!==sr(e)||e==="**"||lu[e]&&lu[t]||t==="%"&&Ir[e]||e==="%"&&Ir[t]||t!==e&&Ir[t]&&Ir[e]||jn[e]&&jn[t])}var vo=new Map([["|>"],["??"],["||"],["&&"],["|"],["^"],["&"],["==","===","!=","!=="],["<",">","<=",">=","in","instanceof"],[">>","<<",">>>"],["+","-"],["*","/","%"],["**"]].flatMap((e,t)=>e.map(r=>[r,t])));function sr(e){return vo.get(e)}function Tu(e){return!!jn[e]||e==="|"||e==="^"||e==="&"}function xu(e){var r;if(e.rest)return!0;let t=Q(e);return((r=v(!1,t,-1))==null?void 0:r.type)==="RestElement"}var Mn=new WeakMap;function Q(e){if(Mn.has(e))return Mn.get(e);let t=[];return e.this&&t.push(e.this),Array.isArray(e.parameters)?t.push(...e.parameters):Array.isArray(e.params)&&t.push(...e.params),e.rest&&t.push(e.rest),Mn.set(e,t),t}function hu(e,t){let{node:r}=e,n=0,s=u=>t(u,n++);r.this&&e.call(s,"this"),Array.isArray(r.parameters)?e.each(s,"parameters"):Array.isArray(r.params)&&e.each(s,"params"),r.rest&&e.call(s,"rest")}var vn=new WeakMap;function le(e){if(vn.has(e))return vn.get(e);if(e.type==="ChainExpression")return le(e.expression);let t=e.arguments;return(e.type==="ImportExpression"||e.type==="TSImportType")&&(t=[e.type==="ImportExpression"?e.source:e.argument],e.options&&t.push(e.options)),vn.set(e,t),t}function Gt(e,t){let{node:r}=e;if(r.type==="ChainExpression")return e.call(()=>Gt(e,t),"expression");r.type==="ImportExpression"||r.type==="TSImportType"?(e.call(n=>t(n,0),r.type==="ImportExpression"?"source":"argument"),r.options&&e.call(n=>t(n,1),"options")):e.each(t,"arguments")}function Gn(e,t){let r=[];if(e.type==="ChainExpression"&&(e=e.expression,r.push("expression")),e.type==="ImportExpression"||e.type==="TSImportType"){if(t===0||t===(e.options?-2:-1))return[...r,e.type==="ImportExpression"?"source":"argument"];if(e.options&&(t===1||t===-1))return[...r,"options"];throw new RangeError("Invalid argument index")}if(t<0&&(t=e.arguments.length+t),t<0||t>=e.arguments.length)throw new RangeError("Invalid argument index");return[...r,"arguments",t]}function or(e){return e.value.trim()==="prettier-ignore"&&!e.unignore}function kt(e){return(e==null?void 0:e.prettierIgnore)||d(e,h.PrettierIgnore)}var h={Leading:2,Trailing:4,Dangling:8,Block:16,Line:32,PrettierIgnore:64,First:128,Last:256},gu=(e,t)=>{if(typeof e=="function"&&(t=e,e=0),e||t)return(r,n,s)=>!(e&h.Leading&&!r.leading||e&h.Trailing&&!r.trailing||e&h.Dangling&&(r.leading||r.trailing)||e&h.Block&&!te(r)||e&h.Line&&!At(r)||e&h.First&&n!==0||e&h.Last&&n!==s.length-1||e&h.PrettierIgnore&&!or(r)||t&&!t(r))};function d(e,t,r){if(!O(e==null?void 0:e.comments))return!1;let n=gu(t,r);return n?e.comments.some(n):!0}function Ve(e,t,r){if(!Array.isArray(e==null?void 0:e.comments))return[];let n=gu(t,r);return n?e.comments.filter(n):e.comments}var me=(e,{originalText:t})=>vt(t,P(e));function yt(e){return w(e)||e.type==="NewExpression"||e.type==="ImportExpression"}function Te(e){return e&&(e.type==="ObjectProperty"||e.type==="Property"&&!bt(e))}var xe=R(["TSAsExpression","TSSatisfiesExpression","AsExpression","AsConstExpression","SatisfiesExpression"]),Oe=R(["TSUnionType","UnionTypeAnnotation"]),qt=R(["TSIntersectionType","IntersectionTypeAnnotation"]),Je=R(["TSConditionalType","ConditionalTypeAnnotation"]);var jo=new Set(["range","raw","comments","leadingComments","trailingComments","innerComments","extra","start","end","loc","flags","errors","tokens"]),Wt=e=>{for(let t of e.quasis)delete t.value};function Su(e,t){var n;if(e.type==="Program"&&delete t.sourceType,(e.type==="BigIntLiteral"||e.type==="BigIntLiteralTypeAnnotation")&&e.value&&(t.value=e.value.toLowerCase()),(e.type==="BigIntLiteral"||e.type==="Literal")&&e.bigint&&(t.bigint=e.bigint.toLowerCase()),e.type==="EmptyStatement"||e.type==="JSXText"||e.type==="JSXExpressionContainer"&&(e.expression.type==="Literal"||e.expression.type==="StringLiteral")&&e.expression.value===" ")return null;if((e.type==="Property"||e.type==="ObjectProperty"||e.type==="MethodDefinition"||e.type==="ClassProperty"||e.type==="ClassMethod"||e.type==="PropertyDefinition"||e.type==="TSDeclareMethod"||e.type==="TSPropertySignature"||e.type==="ObjectTypeProperty"||e.type==="ImportAttribute")&&e.key&&!e.computed){let{key:s}=e;K(s)||ye(s)?t.key=String(s.value):s.type==="Identifier"&&(t.key=s.name)}if(e.type==="JSXElement"&&e.openingElement.name.name==="style"&&e.openingElement.attributes.some(s=>s.type==="JSXAttribute"&&s.name.name==="jsx"))for(let{type:s,expression:u}of t.children)s==="JSXExpressionContainer"&&u.type==="TemplateLiteral"&&Wt(u);e.type==="JSXAttribute"&&e.name.name==="css"&&e.value.type==="JSXExpressionContainer"&&e.value.expression.type==="TemplateLiteral"&&Wt(t.value.expression),e.type==="JSXAttribute"&&((n=e.value)==null?void 0:n.type)==="Literal"&&/["']|&quot;|&apos;/u.test(e.value.value)&&(t.value.value=X(!1,e.value.value,/["']|&quot;|&apos;/gu,'"'));let r=e.expression||e.callee;if(e.type==="Decorator"&&r.type==="CallExpression"&&r.callee.name==="Component"&&r.arguments.length===1){let s=e.expression.arguments[0].properties;for(let[u,i]of t.expression.arguments[0].properties.entries())switch(s[u].key.name){case"styles":U(i.value)&&Wt(i.value.elements[0]);break;case"template":i.value.type==="TemplateLiteral"&&Wt(i.value);break}}e.type==="TaggedTemplateExpression"&&(e.tag.type==="MemberExpression"||e.tag.type==="Identifier"&&(e.tag.name==="gql"||e.tag.name==="graphql"||e.tag.name==="css"||e.tag.name==="md"||e.tag.name==="markdown"||e.tag.name==="html")||e.tag.type==="CallExpression")&&Wt(t.quasi),e.type==="TemplateLiteral"&&Wt(t),e.type==="ChainExpression"&&e.expression.type==="TSNonNullExpression"&&(t.type="TSNonNullExpression",t.expression.type="ChainExpression")}Su.ignoredProperties=jo;var Bu=Su;var Ne="string",ge="array",it="cursor",$e="indent",Ke="align",Qe="trim",fe="group",_e="fill",he="if-break",ze="indent-if-break",Ze="line-suffix",Ge="line-suffix-boundary",oe="line",Se="label",Me="break-parent",jr=new Set([it,$e,Ke,Qe,fe,_e,he,ze,Ze,Ge,oe,Se,Me]);function Ro(e){if(typeof e=="string")return Ne;if(Array.isArray(e))return ge;if(!e)return;let{type:t}=e;if(jr.has(t))return t}var Be=Ro;var Jo=e=>new Intl.ListFormat("en-US",{type:"disjunction"}).format(e);function No(e){let t=e===null?"null":typeof e;if(t!=="string"&&t!=="object")return`Unexpected doc '${t}', 
Expected it to be 'string' or 'object'.`;if(Be(e))throw new Error("doc is valid.");let r=Object.prototype.toString.call(e);if(r!=="[object Object]")return`Unexpected doc '${r}'.`;let n=Jo([...jr].map(s=>`'${s}'`));return`Unexpected doc.type '${e.type}'.
Expected it to be ${n}.`}var qn=class extends Error{name="InvalidDocError";constructor(t){super(No(t)),this.doc=t}},Tt=qn;var bu={};function Go(e,t,r,n){let s=[e];for(;s.length>0;){let u=s.pop();if(u===bu){r(s.pop());continue}r&&s.push(u,bu);let i=Be(u);if(!i)throw new Tt(u);if((t==null?void 0:t(u))!==!1)switch(i){case ge:case _e:{let a=i===ge?u:u.parts;for(let o=a.length,p=o-1;p>=0;--p)s.push(a[p]);break}case he:s.push(u.flatContents,u.breakContents);break;case fe:if(n&&u.expandedStates)for(let a=u.expandedStates.length,o=a-1;o>=0;--o)s.push(u.expandedStates[o]);else s.push(u.contents);break;case Ke:case $e:case ze:case Se:case Ze:s.push(u.contents);break;case Ne:case it:case Qe:case Ge:case oe:case Me:break;default:throw new Tt(u)}}}var pr=Go;function ft(e,t){if(typeof e=="string")return t(e);let r=new Map;return n(e);function n(u){if(r.has(u))return r.get(u);let i=s(u);return r.set(u,i),i}function s(u){switch(Be(u)){case ge:return t(u.map(n));case _e:return t({...u,parts:u.parts.map(n)});case he:return t({...u,breakContents:n(u.breakContents),flatContents:n(u.flatContents)});case fe:{let{expandedStates:i,contents:a}=u;return i?(i=i.map(n),a=i[0]):a=n(a),t({...u,contents:a,expandedStates:i})}case Ke:case $e:case ze:case Se:case Ze:return t({...u,contents:n(u.contents)});case Ne:case it:case Qe:case Ge:case oe:case Me:return t(u);default:throw new Tt(u)}}}function ku(e,t,r){let n=r,s=!1;function u(i){if(s)return!1;let a=t(i);a!==void 0&&(s=!0,n=a)}return pr(e,u),n}function qo(e){if(e.type===fe&&e.break||e.type===oe&&e.hard||e.type===Me)return!0}function re(e){return ku(e,qo,!1)}function Pu(e){if(e.length>0){let t=v(!1,e,-1);!t.expandedStates&&!t.break&&(t.break="propagated")}return null}function Iu(e){let t=new Set,r=[];function n(u){if(u.type===Me&&Pu(r),u.type===fe){if(r.push(u),t.has(u))return!1;t.add(u)}}function s(u){u.type===fe&&r.pop().break&&Pu(r)}pr(e,n,s,!0)}function Wo(e){return e.type===oe&&!e.hard?e.soft?"":" ":e.type===he?e.flatContents:e}function cr(e){return ft(e,Wo)}function Uo(e){switch(Be(e)){case _e:if(e.parts.every(t=>t===""))return"";break;case fe:if(!e.contents&&!e.id&&!e.break&&!e.expandedStates)return"";if(e.contents.type===fe&&e.contents.id===e.id&&e.contents.break===e.break&&e.contents.expandedStates===e.expandedStates)return e.contents;break;case Ke:case $e:case ze:case Ze:if(!e.contents)return"";break;case he:if(!e.flatContents&&!e.breakContents)return"";break;case ge:{let t=[];for(let r of e){if(!r)continue;let[n,...s]=Array.isArray(r)?r:[r];typeof n=="string"&&typeof v(!1,t,-1)=="string"?t[t.length-1]+=n:t.push(n),t.push(...s)}return t.length===0?"":t.length===1?t[0]:t}case Ne:case it:case Qe:case Ge:case oe:case Se:case Me:break;default:throw new Tt(e)}return e}function Ut(e){return ft(e,t=>Uo(t))}function ve(e,t=Rr){return ft(e,r=>typeof r=="string"?b(t,r.split(`
`)):r)}function Xo(e){if(e.type===oe)return!0}function Lu(e){return ku(e,Xo,!1)}function lr(e,t){return e.type===Se?{...e,contents:t(e.contents)}:t(e)}function wu(e){let t=!0;return pr(e,r=>{switch(Be(r)){case Ne:if(r==="")break;case Qe:case Ge:case oe:case Me:return t=!1,!1}}),t}var Wn=()=>{},et=Wn,Un=Wn,Ou=Wn;function D(e){return et(e),{type:$e,contents:e}}function be(e,t){return et(t),{type:Ke,contents:t,n:e}}function l(e,t={}){return et(e),Un(t.expandedStates,!0),{type:fe,id:t.id,contents:e,break:!!t.shouldBreak,expandedStates:t.expandedStates}}function _u(e){return be(Number.NEGATIVE_INFINITY,e)}function Jr(e){return be(-1,e)}function tt(e,t){return l(e[0],{...t,expandedStates:e})}function Nr(e){return Ou(e),{type:_e,parts:e}}function S(e,t="",r={}){return et(e),t!==""&&et(t),{type:he,breakContents:e,flatContents:t,groupId:r.groupId}}function xt(e,t){return et(e),{type:ze,contents:e,groupId:t.groupId,negate:t.negate}}function Xn(e){return et(e),{type:Ze,contents:e}}var je={type:Ge},Ce={type:Me};var Yn={type:oe,hard:!0},Yo={type:oe,hard:!0,literal:!0},x={type:oe},E={type:oe,soft:!0},F=[Yn,Ce],Rr=[Yo,Ce],mr={type:it};function b(e,t){et(e),Un(t);let r=[];for(let n=0;n<t.length;n++)n!==0&&r.push(e),r.push(t[n]);return r}function Mu(e,t,r){et(e);let n=e;if(t>0){for(let s=0;s<Math.floor(t/r);++s)n=D(n);n=be(t%r,n),n=be(Number.NEGATIVE_INFINITY,n)}return n}function at(e,t){return et(t),e?{type:Se,label:e,contents:t}:t}function Ho(e){if(!te(e))return!1;let t=`*${e.value}*`.split(`
`);return t.length>1&&t.every(r=>r.trimStart()[0]==="*")}var Hn=new WeakMap;function Vo(e){return Hn.has(e)||Hn.set(e,Ho(e)),Hn.get(e)}var vu=Vo;function ju(e,t){let r=e.node;if(At(r))return t.originalText.slice(j(r),P(r)).trimEnd();if(vu(r))return $o(r);if(te(r))return["/*",ve(r.value),"*/"];throw new Error("Not a comment: "+JSON.stringify(r))}function $o(e){let t=e.value.split(`
`);return["/*",b(F,t.map((r,n)=>n===0?r.trimEnd():" "+(n<t.length-1?r.trim():r.trimStart()))),"*/"]}var ts={};xr(ts,{endOfLine:()=>rp,ownLine:()=>tp,remaining:()=>np});function Ko(e){let t=e.type||e.kind||"(unknown type)",r=String(e.name||e.id&&(typeof e.id=="object"?e.id.name:e.id)||e.key&&(typeof e.key=="object"?e.key.name:e.key)||e.value&&(typeof e.value=="object"?"":String(e.value))||e.operator||"");return r.length>20&&(r=r.slice(0,19)+"\u2026"),t+(r?" "+r:"")}function Vn(e,t){(e.comments??(e.comments=[])).push(t),t.printed=!1,t.nodeDescription=Ko(e)}function De(e,t){t.leading=!0,t.trailing=!1,Vn(e,t)}function Pe(e,t,r){t.leading=!1,t.trailing=!1,r&&(t.marker=r),Vn(e,t)}function z(e,t){t.leading=!1,t.trailing=!0,Vn(e,t)}function Qo(e,t){let r=null,n=t;for(;n!==r;)r=n,n=Ye(e,n),n=_t(e,n),n=Mt(e,n),n=He(e,n);return n}var ot=Qo;function zo(e,t){let r=ot(e,t);return r===!1?"":e.charAt(r)}var ke=zo;function Zo(e,t,r){for(let n=t;n<r;++n)if(e.charAt(n)===`
`)return!0;return!1}var ie=Zo;var $n=new WeakMap;function ep(e){return $n.has(e)||$n.set(e,te(e)&&e.value[0]==="*"&&/@(?:type|satisfies)\b/u.test(e.value)),$n.get(e)}var Gr=ep;var Ru=(e,t)=>At(e)||!ie(t,j(e),P(e));function tp(e){return[Yu,Nu,Wu,fp,up,Qn,zn,Ju,Gu,Cp,Ep,es,Xu,Ap,qu,Uu,Zn,ip,Bp,Hu].some(t=>t(e))}function rp(e){return[sp,Wu,Nu,Xu,Qn,zn,Ju,Gu,Uu,Dp,Fp,es,xp,Zn,gp,Sp,bp,Hu].some(t=>t(e))}function np(e){return[Yu,Qn,zn,ap,yp,qu,es,mp,lp,Zn,hp].some(t=>t(e))}function It(e,t){let r=(e.body||e.properties).find(({type:n})=>n!=="EmptyStatement");r?De(r,t):Pe(e,t)}function Kn(e,t){e.type==="BlockStatement"?It(e,t):De(e,t)}function sp({comment:e,followingNode:t}){return t&&Gr(e)?(De(t,e),!0):!1}function Qn({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s}){if((r==null?void 0:r.type)!=="IfStatement"||!n)return!1;if(ke(s,P(e))===")")return z(t,e),!0;if(t===r.consequent&&n===r.alternate){let i=ot(s,P(r.consequent));if(j(e)<i||r.alternate.type==="BlockStatement")return t.type==="BlockStatement"?(z(t,e),!0):Ru(e,s)&&!ie(s,j(t),j(e))?(z(t,e),!0):(Pe(r,e),!0)}return n.type==="BlockStatement"?(It(n,e),!0):n.type==="IfStatement"?(Kn(n.consequent,e),!0):r.consequent===n?(De(n,e),!0):!1}function zn({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s}){return(r==null?void 0:r.type)!=="WhileStatement"||!n?!1:ke(s,P(e))===")"?(z(t,e),!0):n.type==="BlockStatement"?(It(n,e),!0):r.body===n?(De(n,e),!0):!1}function Ju({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){return(r==null?void 0:r.type)!=="TryStatement"&&(r==null?void 0:r.type)!=="CatchClause"||!n?!1:r.type==="CatchClause"&&t?(z(t,e),!0):n.type==="BlockStatement"?(It(n,e),!0):n.type==="TryStatement"?(Kn(n.finalizer,e),!0):n.type==="CatchClause"?(Kn(n.body,e),!0):!1}function up({comment:e,enclosingNode:t,followingNode:r}){return G(t)&&(r==null?void 0:r.type)==="Identifier"?(De(t,e),!0):!1}function ip({comment:e,enclosingNode:t,followingNode:r,options:n}){return!n.experimentalTernaries||!((t==null?void 0:t.type)==="ConditionalExpression"||Je(t))?!1:(r==null?void 0:r.type)==="ConditionalExpression"||Je(r)?(Pe(t,e),!0):!1}function Nu({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s,options:u}){let i=t&&!ie(s,P(t),j(e));return(!t||!i)&&((r==null?void 0:r.type)==="ConditionalExpression"||Je(r))&&n?u.experimentalTernaries&&r.alternate===n&&!(te(e)&&!ie(u.originalText,j(e),P(e)))?(Pe(r,e),!0):(De(n,e),!0):!1}function ap({comment:e,precedingNode:t,enclosingNode:r}){return Te(r)&&r.shorthand&&r.key===t&&r.value.type==="AssignmentPattern"?(z(r.value.left,e),!0):!1}var op=new Set(["ClassDeclaration","ClassExpression","DeclareClass","DeclareInterface","InterfaceDeclaration","TSInterfaceDeclaration"]);function Gu({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){if(op.has(r==null?void 0:r.type)){if(O(r.decorators)&&(n==null?void 0:n.type)!=="Decorator")return z(v(!1,r.decorators,-1),e),!0;if(r.body&&n===r.body)return It(r.body,e),!0;if(n){if(r.superClass&&n===r.superClass&&t&&(t===r.id||t===r.typeParameters))return z(t,e),!0;for(let s of["implements","extends","mixins"])if(r[s]&&n===r[s][0])return t&&(t===r.id||t===r.typeParameters||t===r.superClass)?z(t,e):Pe(r,e,s),!0}}return!1}var pp=new Set(["ClassMethod","ClassProperty","PropertyDefinition","TSAbstractPropertyDefinition","TSAbstractMethodDefinition","TSDeclareMethod","MethodDefinition","ClassAccessorProperty","AccessorProperty","TSAbstractAccessorProperty","TSParameterProperty"]);function qu({comment:e,precedingNode:t,enclosingNode:r,text:n}){return r&&t&&ke(n,P(e))==="("&&(r.type==="Property"||r.type==="TSDeclareMethod"||r.type==="TSAbstractMethodDefinition")&&t.type==="Identifier"&&r.key===t&&ke(n,P(t))!==":"?(z(t,e),!0):(t==null?void 0:t.type)==="Decorator"&&pp.has(r==null?void 0:r.type)&&(At(e)||e.placement==="ownLine")?(z(t,e),!0):!1}var cp=new Set(["FunctionDeclaration","FunctionExpression","ClassMethod","MethodDefinition","ObjectMethod"]);function lp({comment:e,precedingNode:t,enclosingNode:r,text:n}){return ke(n,P(e))!=="("?!1:t&&cp.has(r==null?void 0:r.type)?(z(t,e),!0):!1}function mp({comment:e,enclosingNode:t,text:r}){if((t==null?void 0:t.type)!=="ArrowFunctionExpression")return!1;let n=ot(r,P(e));return n!==!1&&r.slice(n,n+2)==="=>"?(Pe(t,e),!0):!1}function yp({comment:e,enclosingNode:t,text:r}){return ke(r,P(e))!==")"?!1:t&&(Vu(t)&&Q(t).length===0||yt(t)&&le(t).length===0)?(Pe(t,e),!0):((t==null?void 0:t.type)==="MethodDefinition"||(t==null?void 0:t.type)==="TSAbstractMethodDefinition")&&Q(t.value).length===0?(Pe(t.value,e),!0):!1}function fp({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s}){return(t==null?void 0:t.type)==="ComponentTypeParameter"&&((r==null?void 0:r.type)==="DeclareComponent"||(r==null?void 0:r.type)==="ComponentTypeAnnotation")&&(n==null?void 0:n.type)!=="ComponentTypeParameter"?(z(t,e),!0):((t==null?void 0:t.type)==="ComponentParameter"||(t==null?void 0:t.type)==="RestElement")&&(r==null?void 0:r.type)==="ComponentDeclaration"&&ke(s,P(e))===")"?(z(t,e),!0):!1}function Wu({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s}){return(t==null?void 0:t.type)==="FunctionTypeParam"&&(r==null?void 0:r.type)==="FunctionTypeAnnotation"&&(n==null?void 0:n.type)!=="FunctionTypeParam"?(z(t,e),!0):((t==null?void 0:t.type)==="Identifier"||(t==null?void 0:t.type)==="AssignmentPattern"||(t==null?void 0:t.type)==="ObjectPattern"||(t==null?void 0:t.type)==="ArrayPattern"||(t==null?void 0:t.type)==="RestElement"||(t==null?void 0:t.type)==="TSParameterProperty")&&Vu(r)&&ke(s,P(e))===")"?(z(t,e),!0):!te(e)&&((r==null?void 0:r.type)==="FunctionDeclaration"||(r==null?void 0:r.type)==="FunctionExpression"||(r==null?void 0:r.type)==="ObjectMethod")&&(n==null?void 0:n.type)==="BlockStatement"&&r.body===n&&ot(s,P(e))===j(n)?(It(n,e),!0):!1}function Uu({comment:e,enclosingNode:t}){return(t==null?void 0:t.type)==="LabeledStatement"?(De(t,e),!0):!1}function Zn({comment:e,enclosingNode:t}){return((t==null?void 0:t.type)==="ContinueStatement"||(t==null?void 0:t.type)==="BreakStatement")&&!t.label?(z(t,e),!0):!1}function Dp({comment:e,precedingNode:t,enclosingNode:r}){return w(r)&&t&&r.callee===t&&r.arguments.length>0?(De(r.arguments[0],e),!0):!1}function Ep({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){return Oe(r)?(or(e)&&(n.prettierIgnore=!0,e.unignore=!0),t?(z(t,e),!0):!1):(Oe(n)&&or(e)&&(n.types[0].prettierIgnore=!0,e.unignore=!0),!1)}function Fp({comment:e,enclosingNode:t}){return Te(t)?(De(t,e),!0):!1}function es({comment:e,enclosingNode:t,ast:r,isLastComment:n}){var s;return((s=r==null?void 0:r.body)==null?void 0:s.length)===0?(n?Pe(r,e):De(r,e),!0):(t==null?void 0:t.type)==="Program"&&t.body.length===0&&!O(t.directives)?(n?Pe(t,e):De(t,e),!0):!1}function Cp({comment:e,enclosingNode:t}){return(t==null?void 0:t.type)==="ForInStatement"||(t==null?void 0:t.type)==="ForOfStatement"?(De(t,e),!0):!1}function Xu({comment:e,precedingNode:t,enclosingNode:r,text:n}){if((r==null?void 0:r.type)==="ImportSpecifier"||(r==null?void 0:r.type)==="ExportSpecifier")return De(r,e),!0;let s=(t==null?void 0:t.type)==="ImportSpecifier"&&(r==null?void 0:r.type)==="ImportDeclaration",u=(t==null?void 0:t.type)==="ExportSpecifier"&&(r==null?void 0:r.type)==="ExportNamedDeclaration";return(s||u)&&ee(n,P(e))?(z(t,e),!0):!1}function Ap({comment:e,enclosingNode:t}){return(t==null?void 0:t.type)==="AssignmentPattern"?(De(t,e),!0):!1}var dp=new Set(["VariableDeclarator","AssignmentExpression","TypeAlias","TSTypeAliasDeclaration"]),Tp=new Set(["ObjectExpression","ArrayExpression","TemplateLiteral","TaggedTemplateExpression","ObjectTypeAnnotation","TSTypeLiteral"]);function xp({comment:e,enclosingNode:t,followingNode:r}){return dp.has(t==null?void 0:t.type)&&r&&(Tp.has(r.type)||te(e))?(De(r,e),!0):!1}function hp({comment:e,enclosingNode:t,followingNode:r,text:n}){return!r&&((t==null?void 0:t.type)==="TSMethodSignature"||(t==null?void 0:t.type)==="TSDeclareFunction"||(t==null?void 0:t.type)==="TSAbstractMethodDefinition")&&ke(n,P(e))===";"?(z(t,e),!0):!1}function Yu({comment:e,enclosingNode:t,followingNode:r}){if(or(e)&&(t==null?void 0:t.type)==="TSMappedType"&&r===t.key)return t.prettierIgnore=!0,e.unignore=!0,!0}function Hu({comment:e,precedingNode:t,enclosingNode:r}){if((r==null?void 0:r.type)==="TSMappedType"&&!t)return Pe(r,e),!0}function gp({comment:e,enclosingNode:t,followingNode:r}){return!t||t.type!=="SwitchCase"||t.test||!r||r!==t.consequent[0]?!1:(r.type==="BlockStatement"&&At(e)?It(r,e):Pe(t,e),!0)}function Sp({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){return Oe(t)&&((r.type==="TSArrayType"||r.type==="ArrayTypeAnnotation")&&!n||qt(r))?(z(v(!1,t.types,-1),e),!0):!1}function Bp({comment:e,enclosingNode:t,precedingNode:r,followingNode:n}){if(((t==null?void 0:t.type)==="ObjectPattern"||(t==null?void 0:t.type)==="ArrayPattern")&&(n==null?void 0:n.type)==="TSTypeAnnotation")return r?z(r,e):Pe(t,e),!0}function bp({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s}){return!n&&(r==null?void 0:r.type)==="UnaryExpression"&&((t==null?void 0:t.type)==="LogicalExpression"||(t==null?void 0:t.type)==="BinaryExpression")&&ie(s,j(r.argument),j(t.right))&&Ru(e,s)&&!ie(s,j(t.right),j(e))?(z(t.right,e),!0):!1}var Vu=R(["ArrowFunctionExpression","FunctionExpression","FunctionDeclaration","ObjectMethod","ClassMethod","TSDeclareFunction","TSCallSignatureDeclaration","TSConstructSignatureDeclaration","TSMethodSignature","TSConstructorType","TSFunctionType","TSDeclareMethod"]);var Pp=new Set(["EmptyStatement","TemplateElement","TSEmptyBodyFunctionExpression","ChainExpression"]);function kp(e){return!Pp.has(e.type)}function Ip(e,t){var r;if((t.parser==="typescript"||t.parser==="flow"||t.parser==="hermes"||t.parser==="acorn"||t.parser==="oxc"||t.parser==="oxc-ts"||t.parser==="espree"||t.parser==="meriyah"||t.parser==="__babel_estree")&&e.type==="MethodDefinition"&&((r=e.value)==null?void 0:r.type)==="FunctionExpression"&&Q(e.value).length===0&&!e.value.returnType&&!O(e.value.typeParameters)&&e.value.body)return[...e.decorators||[],e.key,e.value.body]}function rs(e){let{node:t,parent:r}=e;return(Y(t)||r&&(r.type==="JSXSpreadAttribute"||r.type==="JSXSpreadChild"||Oe(r)||(r.type==="ClassDeclaration"||r.type==="ClassExpression")&&r.superClass===t))&&(!kt(t)||Oe(r))}function Lp(e,{parser:t}){if(t==="flow"||t==="hermes"||t==="babel-flow")return e=X(!1,e,/[\s(]/gu,""),e===""||e==="/*"||e==="/*::"}function $u(e){switch(e){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}var Ie=Symbol("MODE_BREAK"),pt=Symbol("MODE_FLAT"),Xt=Symbol("cursor"),ns=Symbol("DOC_FILL_PRINTED_LENGTH");function Ku(){return{value:"",length:0,queue:[]}}function wp(e,t){return ss(e,{type:"indent"},t)}function Op(e,t,r){return t===Number.NEGATIVE_INFINITY?e.root||Ku():t<0?ss(e,{type:"dedent"},r):t?t.type==="root"?{...e,root:e}:ss(e,{type:typeof t=="string"?"stringAlign":"numberAlign",n:t},r):e}function ss(e,t,r){let n=t.type==="dedent"?e.queue.slice(0,-1):[...e.queue,t],s="",u=0,i=0,a=0;for(let c of n)switch(c.type){case"indent":m(),r.useTabs?o(1):p(r.tabWidth);break;case"stringAlign":m(),s+=c.n,u+=c.n.length;break;case"numberAlign":i+=1,a+=c.n;break;default:throw new Error(`Unexpected type '${c.type}'`)}return y(),{...e,value:s,length:u,queue:n};function o(c){s+="	".repeat(c),u+=r.tabWidth*c}function p(c){s+=" ".repeat(c),u+=c}function m(){r.useTabs?f():y()}function f(){i>0&&o(i),C()}function y(){a>0&&p(a),C()}function C(){i=0,a=0}}function us(e){let t=0,r=0,n=e.length;e:for(;n--;){let s=e[n];if(s===Xt){r++;continue}for(let u=s.length-1;u>=0;u--){let i=s[u];if(i===" "||i==="	")t++;else{e[n]=s.slice(0,u+1);break e}}}if(t>0||r>0)for(e.length=n+1;r-- >0;)e.push(Xt);return t}function qr(e,t,r,n,s,u){if(r===Number.POSITIVE_INFINITY)return!0;let i=t.length,a=[e],o=[];for(;r>=0;){if(a.length===0){if(i===0)return!0;a.push(t[--i]);continue}let{mode:p,doc:m}=a.pop(),f=Be(m);switch(f){case Ne:o.push(m),r-=st(m);break;case ge:case _e:{let y=f===ge?m:m.parts,C=m[ns]??0;for(let c=y.length-1;c>=C;c--)a.push({mode:p,doc:y[c]});break}case $e:case Ke:case ze:case Se:a.push({mode:p,doc:m.contents});break;case Qe:r+=us(o);break;case fe:{if(u&&m.break)return!1;let y=m.break?Ie:p,C=m.expandedStates&&y===Ie?v(!1,m.expandedStates,-1):m.contents;a.push({mode:y,doc:C});break}case he:{let C=(m.groupId?s[m.groupId]||pt:p)===Ie?m.breakContents:m.flatContents;C&&a.push({mode:p,doc:C});break}case oe:if(p===Ie||m.hard)return!0;m.soft||(o.push(" "),r--);break;case Ze:n=!0;break;case Ge:if(n)return!1;break}}return!1}function is(e,t){let r={},n=t.printWidth,s=$u(t.endOfLine),u=0,i=[{ind:Ku(),mode:Ie,doc:e}],a=[],o=!1,p=[],m=0;for(Iu(e);i.length>0;){let{ind:y,mode:C,doc:c}=i.pop();switch(Be(c)){case Ne:{let A=s!==`
`?X(!1,c,`
`,s):c;a.push(A),i.length>0&&(u+=st(A));break}case ge:for(let A=c.length-1;A>=0;A--)i.push({ind:y,mode:C,doc:c[A]});break;case it:if(m>=2)throw new Error("There are too many 'cursor' in doc.");a.push(Xt),m++;break;case $e:i.push({ind:wp(y,t),mode:C,doc:c.contents});break;case Ke:i.push({ind:Op(y,c.n,t),mode:C,doc:c.contents});break;case Qe:u-=us(a);break;case fe:switch(C){case pt:if(!o){i.push({ind:y,mode:c.break?Ie:pt,doc:c.contents});break}case Ie:{o=!1;let A={ind:y,mode:pt,doc:c.contents},T=n-u,B=p.length>0;if(!c.break&&qr(A,i,T,B,r))i.push(A);else if(c.expandedStates){let g=v(!1,c.expandedStates,-1);if(c.break){i.push({ind:y,mode:Ie,doc:g});break}else for(let _=1;_<c.expandedStates.length+1;_++)if(_>=c.expandedStates.length){i.push({ind:y,mode:Ie,doc:g});break}else{let M=c.expandedStates[_],J={ind:y,mode:pt,doc:M};if(qr(J,i,T,B,r)){i.push(J);break}}}else i.push({ind:y,mode:Ie,doc:c.contents});break}}c.id&&(r[c.id]=v(!1,i,-1).mode);break;case _e:{let A=n-u,T=c[ns]??0,{parts:B}=c,g=B.length-T;if(g===0)break;let _=B[T+0],M=B[T+1],J={ind:y,mode:pt,doc:_},I={ind:y,mode:Ie,doc:_},q=qr(J,[],A,p.length>0,r,!0);if(g===1){q?i.push(J):i.push(I);break}let k={ind:y,mode:pt,doc:M},W={ind:y,mode:Ie,doc:M};if(g===2){q?i.push(k,J):i.push(W,I);break}let ne=B[T+2],Z={ind:y,mode:C,doc:{...c,[ns]:T+2}};qr({ind:y,mode:pt,doc:[_,M,ne]},[],A,p.length>0,r,!0)?i.push(Z,k,J):q?i.push(Z,W,J):i.push(Z,W,I);break}case he:case ze:{let A=c.groupId?r[c.groupId]:C;if(A===Ie){let T=c.type===he?c.breakContents:c.negate?c.contents:D(c.contents);T&&i.push({ind:y,mode:C,doc:T})}if(A===pt){let T=c.type===he?c.flatContents:c.negate?D(c.contents):c.contents;T&&i.push({ind:y,mode:C,doc:T})}break}case Ze:p.push({ind:y,mode:C,doc:c.contents});break;case Ge:p.length>0&&i.push({ind:y,mode:C,doc:Yn});break;case oe:switch(C){case pt:if(c.hard)o=!0;else{c.soft||(a.push(" "),u+=1);break}case Ie:if(p.length>0){i.push({ind:y,mode:C,doc:c},...p.reverse()),p.length=0;break}c.literal?y.root?(a.push(s,y.root.value),u=y.root.length):(a.push(s),u=0):(u-=us(a),a.push(s+y.value),u=y.length);break}break;case Se:i.push({ind:y,mode:C,doc:c.contents});break;case Me:break;default:throw new Tt(c)}i.length===0&&p.length>0&&(i.push(...p.reverse()),p.length=0)}let f=a.indexOf(Xt);if(f!==-1){let y=a.indexOf(Xt,f+1);if(y===-1)return{formatted:a.filter(T=>T!==Xt).join("")};let C=a.slice(0,f).join(""),c=a.slice(f+1,y).join(""),A=a.slice(y+1).join("");return{formatted:C+c+A,cursorNodeStart:C.length,cursorNodeText:c}}return{formatted:a.join("")}}function _p(e,t,r=0){let n=0;for(let s=r;s<e.length;++s)e[s]==="	"?n=n+t-n%t:n++;return n}var Qu=_p;function Mp(e,t){let r=e.lastIndexOf(`
`);return r===-1?0:Qu(e.slice(r+1).match(/^[\t ]*/u)[0],t)}var zu=Mp;function Wr(e,t,r){let{node:n}=e;if(n.type==="TemplateLiteral"&&Rp(e)){let p=vp(e,t,r);if(p)return p}let u="expressions";n.type==="TSTemplateLiteralType"&&(u="types");let i=[],a=e.map(r,u);i.push(je,"`");let o=0;return e.each(({index:p,node:m})=>{if(i.push(r()),m.tail)return;let{tabWidth:f}=t,y=m.value.raw,C=y.includes(`
`)?zu(y,f):o;o=C;let c=a[p],A=n[u][p],T=ie(t.originalText,P(m),j(n.quasis[p+1]));if(!T){let g=is(c,{...t,printWidth:Number.POSITIVE_INFINITY}).formatted;g.includes(`
`)?T=!0:c=g}T&&(d(A)||A.type==="Identifier"||G(A)||A.type==="ConditionalExpression"||A.type==="SequenceExpression"||xe(A)||Fe(A))&&(c=[D([E,c]),E]);let B=C===0&&y.endsWith(`
`)?be(Number.NEGATIVE_INFINITY,c):Mu(c,C,f);i.push(l(["${",B,je,"}"]))},"quasis"),i.push("`"),i}function Zu(e,t,r){let n=r("quasi"),{node:s}=e,u="",i=Ve(s.quasi,h.Leading)[0];return i&&(ie(t.originalText,P(s.typeArguments??s.typeParameters??s.tag),j(i))?u=E:u=" "),at(n.label&&{tagged:!0,...n.label},[r("tag"),r(s.typeArguments?"typeArguments":"typeParameters"),u,je,n])}function vp(e,t,r){let{node:n}=e,s=n.quasis[0].value.raw.trim().split(/\s*\|\s*/u);if(s.length>1||s.some(u=>u.length>0)){t.__inJestEach=!0;let u=e.map(r,"expressions");t.__inJestEach=!1;let i=[],a=u.map(y=>"${"+is(y,{...t,printWidth:Number.POSITIVE_INFINITY,endOfLine:"lf"}).formatted+"}"),o=[{hasLineBreak:!1,cells:[]}];for(let y=1;y<n.quasis.length;y++){let C=v(!1,o,-1),c=a[y-1];C.cells.push(c),c.includes(`
`)&&(C.hasLineBreak=!0),n.quasis[y].value.raw.includes(`
`)&&o.push({hasLineBreak:!1,cells:[]})}let p=Math.max(s.length,...o.map(y=>y.cells.length)),m=Array.from({length:p}).fill(0),f=[{cells:s},...o.filter(y=>y.cells.length>0)];for(let{cells:y}of f.filter(C=>!C.hasLineBreak))for(let[C,c]of y.entries())m[C]=Math.max(m[C],st(c));return i.push(je,"`",D([F,b(F,f.map(y=>b(" | ",y.cells.map((C,c)=>y.hasLineBreak?C:C+" ".repeat(m[c]-st(C))))))]),F,"`"),i}}function jp(e,t){let{node:r}=e,n=t();return d(r)&&(n=l([D([E,n]),E])),["${",n,je,"}"]}function Yt(e,t){return e.map(r=>jp(r,t),"expressions")}function Ur(e,t){return ft(e,r=>typeof r=="string"?t?X(!1,r,/(\\*)`/gu,"$1$1\\`"):as(r):r)}function as(e){return X(!1,e,/([\\`]|\$\{)/gu,String.raw`\$1`)}function Rp({node:e,parent:t}){let r=/^[fx]?(?:describe|it|test)$/u;return t.type==="TaggedTemplateExpression"&&t.quasi===e&&t.tag.type==="MemberExpression"&&t.tag.property.type==="Identifier"&&t.tag.property.name==="each"&&(t.tag.object.type==="Identifier"&&r.test(t.tag.object.name)||t.tag.object.type==="MemberExpression"&&t.tag.object.property.type==="Identifier"&&(t.tag.object.property.name==="only"||t.tag.object.property.name==="skip")&&t.tag.object.object.type==="Identifier"&&r.test(t.tag.object.object.name))}var ps=[(e,t)=>e.type==="ObjectExpression"&&t==="properties",(e,t)=>e.type==="CallExpression"&&e.callee.type==="Identifier"&&e.callee.name==="Component"&&t==="arguments",(e,t)=>e.type==="Decorator"&&t==="expression"];function ei(e){let t=n=>n.type==="TemplateLiteral",r=(n,s)=>Te(n)&&!n.computed&&n.key.type==="Identifier"&&n.key.name==="styles"&&s==="value";return e.match(t,(n,s)=>U(n)&&s==="elements",r,...ps)||e.match(t,r,...ps)}function ti(e){return e.match(t=>t.type==="TemplateLiteral",(t,r)=>Te(t)&&!t.computed&&t.key.type==="Identifier"&&t.key.name==="template"&&r==="value",...ps)}function os(e,t){return d(e,h.Block|h.Leading,({value:r})=>r===` ${t} `)}function Xr({node:e,parent:t},r){return os(e,r)||Jp(t)&&os(t,r)||t.type==="ExpressionStatement"&&os(t,r)}function Jp(e){return e.type==="AsConstExpression"||e.type==="TSAsExpression"&&e.typeAnnotation.type==="TSTypeReference"&&e.typeAnnotation.typeName.type==="Identifier"&&e.typeAnnotation.typeName.name==="const"}async function Np(e,t,r){let{node:n}=r,s=n.quasis.map(m=>m.value.raw),u=0,i=s.reduce((m,f,y)=>y===0?f:m+"@prettier-placeholder-"+u+++"-id"+f,""),a=await e(i,{parser:"scss"}),o=Yt(r,t),p=Gp(a,o);if(!p)throw new Error("Couldn't insert all the expressions");return["`",D([F,p]),E,"`"]}function Gp(e,t){if(!O(t))return e;let r=0,n=ft(Ut(e),s=>typeof s!="string"||!s.includes("@prettier-placeholder")?s:s.split(/@prettier-placeholder-(\d+)-id/u).map((u,i)=>i%2===0?ve(u):(r++,t[u])));return t.length===r?n:null}function qp({node:e,parent:t,grandparent:r}){return r&&e.quasis&&t.type==="JSXExpressionContainer"&&r.type==="JSXElement"&&r.openingElement.name.name==="style"&&r.openingElement.attributes.some(n=>n.type==="JSXAttribute"&&n.name.name==="jsx")||(t==null?void 0:t.type)==="TaggedTemplateExpression"&&t.tag.type==="Identifier"&&t.tag.name==="css"||(t==null?void 0:t.type)==="TaggedTemplateExpression"&&t.tag.type==="MemberExpression"&&t.tag.object.name==="css"&&(t.tag.property.name==="global"||t.tag.property.name==="resolve")}function Yr(e){return e.type==="Identifier"&&e.name==="styled"}function ri(e){return/^[A-Z]/u.test(e.object.name)&&e.property.name==="extend"}function Wp({parent:e}){if(!e||e.type!=="TaggedTemplateExpression")return!1;let t=e.tag.type==="ParenthesizedExpression"?e.tag.expression:e.tag;switch(t.type){case"MemberExpression":return Yr(t.object)||ri(t);case"CallExpression":return Yr(t.callee)||t.callee.type==="MemberExpression"&&(t.callee.object.type==="MemberExpression"&&(Yr(t.callee.object.object)||ri(t.callee.object))||t.callee.object.type==="CallExpression"&&Yr(t.callee.object.callee));case"Identifier":return t.name==="css";default:return!1}}function Up({parent:e,grandparent:t}){return(t==null?void 0:t.type)==="JSXAttribute"&&e.type==="JSXExpressionContainer"&&t.name.type==="JSXIdentifier"&&t.name.name==="css"}function Xp(e){if(qp(e)||Wp(e)||Up(e)||ei(e))return Np}var ni=Xp;async function Yp(e,t,r){let{node:n}=r,s=n.quasis.length,u=Yt(r,t),i=[];for(let a=0;a<s;a++){let o=n.quasis[a],p=a===0,m=a===s-1,f=o.value.cooked,y=f.split(`
`),C=y.length,c=u[a],A=C>2&&y[0].trim()===""&&y[1].trim()==="",T=C>2&&y[C-1].trim()===""&&y[C-2].trim()==="",B=y.every(_=>/^\s*(?:#[^\n\r]*)?$/u.test(_));if(!m&&/#[^\n\r]*$/u.test(y[C-1]))return null;let g=null;B?g=Hp(y):g=await e(f,{parser:"graphql"}),g?(g=Ur(g,!1),!p&&A&&i.push(""),i.push(g),!m&&T&&i.push("")):!p&&!m&&A&&i.push(""),c&&i.push(c)}return["`",D([F,b(F,i)]),F,"`"]}function Hp(e){let t=[],r=!1,n=e.map(s=>s.trim());for(let[s,u]of n.entries())u!==""&&(n[s-1]===""&&r?t.push([F,u]):t.push(u),r=!0);return t.length===0?null:b(F,t)}function Vp({node:e,parent:t}){return Xr({node:e,parent:t},"GraphQL")||t&&(t.type==="TaggedTemplateExpression"&&(t.tag.type==="MemberExpression"&&t.tag.object.name==="graphql"&&t.tag.property.name==="experimental"||t.tag.type==="Identifier"&&(t.tag.name==="gql"||t.tag.name==="graphql"))||t.type==="CallExpression"&&t.callee.type==="Identifier"&&t.callee.name==="graphql")}function $p(e){if(Vp(e))return Yp}var si=$p;var cs=0;async function ui(e,t,r,n,s){let{node:u}=n,i=cs;cs=cs+1>>>0;let a=B=>`PRETTIER_HTML_PLACEHOLDER_${B}_${i}_IN_JS`,o=u.quasis.map((B,g,_)=>g===_.length-1?B.value.cooked:B.value.cooked+a(g)).join(""),p=Yt(n,r),m=new RegExp(a(String.raw`(\d+)`),"gu"),f=0,y=await t(o,{parser:e,__onHtmlRoot(B){f=B.children.length}}),C=ft(y,B=>{if(typeof B!="string")return B;let g=[],_=B.split(m);for(let M=0;M<_.length;M++){let J=_[M];if(M%2===0){J&&(J=as(J),s.__embeddedInHtml&&(J=X(!1,J,/<\/(?=script\b)/giu,String.raw`<\/`)),g.push(J));continue}let I=Number(J);g.push(p[I])}return g}),c=/^\s/u.test(o)?" ":"",A=/\s$/u.test(o)?" ":"",T=s.htmlWhitespaceSensitivity==="ignore"?F:c&&A?x:null;return T?l(["`",D([T,l(C)]),T,"`"]):at({hug:!1},l(["`",c,f>1?D(l(C)):l(C),A,"`"]))}function Kp(e){return Xr(e,"HTML")||e.match(t=>t.type==="TemplateLiteral",(t,r)=>t.type==="TaggedTemplateExpression"&&t.tag.type==="Identifier"&&t.tag.name==="html"&&r==="quasi")}var Qp=ui.bind(void 0,"html"),zp=ui.bind(void 0,"angular");function Zp(e){if(Kp(e))return Qp;if(ti(e))return zp}var ii=Zp;async function ec(e,t,r){let{node:n}=r,s=X(!1,n.quasis[0].value.raw,/((?:\\\\)*)\\`/gu,(o,p)=>"\\".repeat(p.length/2)+"`"),u=tc(s),i=u!=="";i&&(s=X(!1,s,new RegExp(`^${u}`,"gmu"),""));let a=Ur(await e(s,{parser:"markdown",__inJsTemplate:!0}),!0);return["`",i?D([E,a]):[Rr,_u(a)],E,"`"]}function tc(e){let t=e.match(/^([^\S\n]*)\S/mu);return t===null?"":t[1]}function rc(e){if(nc(e))return ec}function nc({node:e,parent:t}){return(t==null?void 0:t.type)==="TaggedTemplateExpression"&&e.quasis.length===1&&t.tag.type==="Identifier"&&(t.tag.name==="md"||t.tag.name==="markdown")}var ai=rc;function sc(e){let{node:t}=e;if(t.type!=="TemplateLiteral"||uc(t))return;let r;for(let n of[ni,si,ii,ai])if(r=n(e),!!r)return t.quasis.length===1&&t.quasis[0].value.raw.trim()===""?"``":async(...s)=>{let u=await r(...s);return u&&at({embed:!0,...u.label},u)}}function uc({quasis:e}){return e.some(({value:{cooked:t}})=>t===null)}var oi=sc;var ic=/\*\/$/,ac=/^\/\*\*?/,mi=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,oc=/(^|\s+)\/\/([^\n\r]*)/g,pi=/^(\r?\n)+/,pc=/(?:^|\r?\n) *(@[^\n\r]*?) *\r?\n *(?![^\n\r@]*\/\/[^]*)([^\s@][^\n\r@]+?) *\r?\n/g,ci=/(?:^|\r?\n) *@(\S+) *([^\n\r]*)/g,cc=/(\r?\n|^) *\* ?/g,yi=[];function fi(e){let t=e.match(mi);return t?t[0].trimStart():""}function Di(e){let t=e.match(mi),r=t==null?void 0:t[0];return r==null?e:e.slice(r.length)}function Ei(e){let t=`
`;e=X(!1,e.replace(ac,"").replace(ic,""),cc,"$1");let r="";for(;r!==e;)r=e,e=X(!1,e,pc,`${t}$1 $2${t}`);e=e.replace(pi,"").trimEnd();let n=Object.create(null),s=X(!1,e,ci,"").replace(pi,"").trimEnd(),u;for(;u=ci.exec(e);){let i=X(!1,u[2],oc,"");if(typeof n[u[1]]=="string"||Array.isArray(n[u[1]])){let a=n[u[1]];n[u[1]]=[...yi,...Array.isArray(a)?a:[a],i]}else n[u[1]]=i}return{comments:s,pragmas:n}}function Fi({comments:e="",pragmas:t={}}){let r=`
`,n="/**",s=" *",u=" */",i=Object.keys(t),a=i.flatMap(p=>li(p,t[p])).map(p=>`${s} ${p}${r}`).join("");if(!e){if(i.length===0)return"";if(i.length===1&&!Array.isArray(t[i[0]])){let p=t[i[0]];return`${n} ${li(i[0],p)[0]}${u}`}}let o=e.split(r).map(p=>`${s} ${p}`).join(r)+r;return n+r+(e?o:"")+(e&&i.length>0?s+r:"")+a+u}function li(e,t){return[...yi,...Array.isArray(t)?t:[t]].map(r=>`@${e} ${r}`.trim())}var Ci="format";function lc(e){if(!e.startsWith("#!"))return"";let t=e.indexOf(`
`);return t===-1?e:e.slice(0,t)}var Ai=lc;function mc(e){let t=Ai(e);t&&(e=e.slice(t.length+1));let r=fi(e),{pragmas:n,comments:s}=Ei(r);return{shebang:t,text:e,pragmas:n,comments:s}}function di(e){let{shebang:t,text:r,pragmas:n,comments:s}=mc(e),u=Di(r),i=Fi({pragmas:{[Ci]:"",...n},comments:s.trimStart()});return(t?`${t}
`:"")+i+(u.startsWith(`
`)?`
`:`

`)+u}function yc(e,t){let{originalText:r,[Symbol.for("comments")]:n,locStart:s,locEnd:u,[Symbol.for("printedComments")]:i}=t,{node:a}=e,o=s(a),p=u(a);for(let m of n)s(m)>=o&&u(m)<=p&&i.add(m);return r.slice(o,p)}var Ti=yc;function ls(e,t){var u,i,a,o,p,m,f,y,C;if(e.isRoot)return!1;let{node:r,key:n,parent:s}=e;if(t.__isInHtmlInterpolation&&!t.bracketSpacing&&Fc(r)&&yr(e))return!0;if(fc(r))return!1;if(r.type==="Identifier"){if((u=r.extra)!=null&&u.parenthesized&&/^PRETTIER_HTML_PLACEHOLDER_\d+_\d+_IN_JS$/u.test(r.name)||n==="left"&&(r.name==="async"&&!s.await||r.name==="let")&&s.type==="ForOfStatement")return!0;if(r.name==="let"){let c=(i=e.findAncestor(A=>A.type==="ForOfStatement"))==null?void 0:i.left;if(c&&pe(c,A=>A===r))return!0}if(n==="object"&&r.name==="let"&&s.type==="MemberExpression"&&s.computed&&!s.optional){let c=e.findAncestor(T=>T.type==="ExpressionStatement"||T.type==="ForStatement"||T.type==="ForInStatement"),A=c?c.type==="ExpressionStatement"?c.expression:c.type==="ForStatement"?c.init:c.left:void 0;if(A&&pe(A,T=>T===r))return!0}if(n==="expression")switch(r.name){case"await":case"interface":case"module":case"using":case"yield":case"let":case"component":case"hook":case"type":{let c=e.findAncestor(A=>!xe(A));if(c!==s&&c.type==="ExpressionStatement")return!0}}return!1}if(r.type==="ObjectExpression"||r.type==="FunctionExpression"||r.type==="ClassExpression"||r.type==="DoExpression"){let c=(a=e.findAncestor(A=>A.type==="ExpressionStatement"))==null?void 0:a.expression;if(c&&pe(c,A=>A===r))return!0}if(r.type==="ObjectExpression"){let c=(o=e.findAncestor(A=>A.type==="ArrowFunctionExpression"))==null?void 0:o.body;if(c&&c.type!=="SequenceExpression"&&c.type!=="AssignmentExpression"&&pe(c,A=>A===r))return!0}switch(s.type){case"ParenthesizedExpression":return!1;case"ClassDeclaration":case"ClassExpression":if(n==="superClass"&&(r.type==="ArrowFunctionExpression"||r.type==="AssignmentExpression"||r.type==="AwaitExpression"||r.type==="BinaryExpression"||r.type==="ConditionalExpression"||r.type==="LogicalExpression"||r.type==="NewExpression"||r.type==="ObjectExpression"||r.type==="SequenceExpression"||r.type==="TaggedTemplateExpression"||r.type==="UnaryExpression"||r.type==="UpdateExpression"||r.type==="YieldExpression"||r.type==="TSNonNullExpression"||r.type==="ClassExpression"&&O(r.decorators)))return!0;break;case"ExportDefaultDeclaration":return xi(e,t)||r.type==="SequenceExpression";case"Decorator":if(n==="expression"&&!Ac(r))return!0;break;case"TypeAnnotation":if(e.match(void 0,void 0,(c,A)=>A==="returnType"&&c.type==="ArrowFunctionExpression")&&Ec(r))return!0;break;case"BinaryExpression":if(n==="left"&&(s.operator==="in"||s.operator==="instanceof")&&r.type==="UnaryExpression")return!0;break;case"VariableDeclarator":if(n==="init"&&e.match(void 0,void 0,(c,A)=>A==="declarations"&&c.type==="VariableDeclaration",(c,A)=>A==="left"&&c.type==="ForInStatement"))return!0;break}switch(r.type){case"UpdateExpression":if(s.type==="UnaryExpression")return r.prefix&&(r.operator==="++"&&s.operator==="+"||r.operator==="--"&&s.operator==="-");case"UnaryExpression":switch(s.type){case"UnaryExpression":return r.operator===s.operator&&(r.operator==="+"||r.operator==="-");case"BindExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return n==="object";case"TaggedTemplateExpression":return!0;case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"BinaryExpression":return n==="left"&&s.operator==="**";case"TSNonNullExpression":return!0;default:return!1}case"BinaryExpression":if(s.type==="UpdateExpression"||r.operator==="in"&&Dc(e))return!0;if(r.operator==="|>"&&((p=r.extra)!=null&&p.parenthesized)){let c=e.grandparent;if(c.type==="BinaryExpression"&&c.operator==="|>")return!0}case"TSTypeAssertion":case"TSAsExpression":case"TSSatisfiesExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":case"LogicalExpression":switch(s.type){case"TSAsExpression":case"TSSatisfiesExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":return!xe(r);case"ConditionalExpression":return xe(r)||fu(r);case"CallExpression":case"NewExpression":case"OptionalCallExpression":return n==="callee";case"ClassExpression":case"ClassDeclaration":return n==="superClass";case"TSTypeAssertion":case"TaggedTemplateExpression":case"UnaryExpression":case"JSXSpreadAttribute":case"SpreadElement":case"BindExpression":case"AwaitExpression":case"TSNonNullExpression":case"UpdateExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return n==="object";case"AssignmentExpression":case"AssignmentPattern":return n==="left"&&(r.type==="TSTypeAssertion"||xe(r));case"LogicalExpression":if(r.type==="LogicalExpression")return s.operator!==r.operator;case"BinaryExpression":{let{operator:c,type:A}=r;if(!c&&A!=="TSTypeAssertion")return!0;let T=sr(c),B=s.operator,g=sr(B);return g>T||n==="right"&&g===T||g===T&&!ar(B,c)?!0:g<T&&c==="%"?B==="+"||B==="-":!!Tu(B)}default:return!1}case"SequenceExpression":return s.type!=="ForStatement";case"YieldExpression":if(s.type==="AwaitExpression"||s.type==="TSTypeAssertion")return!0;case"AwaitExpression":switch(s.type){case"TaggedTemplateExpression":case"UnaryExpression":case"LogicalExpression":case"SpreadElement":case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":case"BindExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return n==="object";case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"ConditionalExpression":return n==="test";case"BinaryExpression":return!(!r.argument&&s.operator==="|>");default:return!1}case"TSFunctionType":if(e.match(c=>c.type==="TSFunctionType",(c,A)=>A==="typeAnnotation"&&c.type==="TSTypeAnnotation",(c,A)=>A==="returnType"&&c.type==="ArrowFunctionExpression"))return!0;case"TSConditionalType":case"TSConstructorType":case"ConditionalTypeAnnotation":if(n==="extendsType"&&Je(r)&&s.type===r.type||n==="checkType"&&Je(s))return!0;if(n==="extendsType"&&s.type==="TSConditionalType"){let{typeAnnotation:c}=r.returnType||r.typeAnnotation;if(c.type==="TSTypePredicate"&&c.typeAnnotation&&(c=c.typeAnnotation.typeAnnotation),c.type==="TSInferType"&&c.typeParameter.constraint)return!0}case"TSUnionType":case"TSIntersectionType":if((Oe(s)||qt(s))&&s.types.length>1&&(!r.types||r.types.length>1))return!0;case"TSInferType":if(r.type==="TSInferType"){if(s.type==="TSRestType")return!1;if(n==="types"&&(s.type==="TSUnionType"||s.type==="TSIntersectionType")&&r.typeParameter.type==="TSTypeParameter"&&r.typeParameter.constraint)return!0}case"TSTypeOperator":return s.type==="TSArrayType"||s.type==="TSOptionalType"||s.type==="TSRestType"||n==="objectType"&&s.type==="TSIndexedAccessType"||s.type==="TSTypeOperator"||s.type==="TSTypeAnnotation"&&e.grandparent.type.startsWith("TSJSDoc");case"TSTypeQuery":return n==="objectType"&&s.type==="TSIndexedAccessType"||n==="elementType"&&s.type==="TSArrayType";case"TypeOperator":return s.type==="ArrayTypeAnnotation"||s.type==="NullableTypeAnnotation"||n==="objectType"&&(s.type==="IndexedAccessType"||s.type==="OptionalIndexedAccessType")||s.type==="TypeOperator";case"TypeofTypeAnnotation":return n==="objectType"&&(s.type==="IndexedAccessType"||s.type==="OptionalIndexedAccessType")||n==="elementType"&&s.type==="ArrayTypeAnnotation";case"ArrayTypeAnnotation":return s.type==="NullableTypeAnnotation";case"IntersectionTypeAnnotation":case"UnionTypeAnnotation":return s.type==="TypeOperator"||s.type==="ArrayTypeAnnotation"||s.type==="NullableTypeAnnotation"||s.type==="IntersectionTypeAnnotation"||s.type==="UnionTypeAnnotation"||n==="objectType"&&(s.type==="IndexedAccessType"||s.type==="OptionalIndexedAccessType");case"InferTypeAnnotation":case"NullableTypeAnnotation":return s.type==="ArrayTypeAnnotation"||n==="objectType"&&(s.type==="IndexedAccessType"||s.type==="OptionalIndexedAccessType");case"ComponentTypeAnnotation":case"FunctionTypeAnnotation":{if(r.type==="ComponentTypeAnnotation"&&(r.rendersType===null||r.rendersType===void 0))return!1;if(e.match(void 0,(A,T)=>T==="typeAnnotation"&&A.type==="TypeAnnotation",(A,T)=>T==="returnType"&&A.type==="ArrowFunctionExpression")||e.match(void 0,(A,T)=>T==="typeAnnotation"&&A.type==="TypePredicate",(A,T)=>T==="typeAnnotation"&&A.type==="TypeAnnotation",(A,T)=>T==="returnType"&&A.type==="ArrowFunctionExpression"))return!0;let c=s.type==="NullableTypeAnnotation"?e.grandparent:s;return c.type==="UnionTypeAnnotation"||c.type==="IntersectionTypeAnnotation"||c.type==="ArrayTypeAnnotation"||n==="objectType"&&(c.type==="IndexedAccessType"||c.type==="OptionalIndexedAccessType")||n==="checkType"&&s.type==="ConditionalTypeAnnotation"||n==="extendsType"&&s.type==="ConditionalTypeAnnotation"&&((m=r.returnType)==null?void 0:m.type)==="InferTypeAnnotation"&&((f=r.returnType)==null?void 0:f.typeParameter.bound)||c.type==="NullableTypeAnnotation"||s.type==="FunctionTypeParam"&&s.name===null&&Q(r).some(A=>{var T;return((T=A.typeAnnotation)==null?void 0:T.type)==="NullableTypeAnnotation"})}case"OptionalIndexedAccessType":return n==="objectType"&&s.type==="IndexedAccessType";case"StringLiteral":case"NumericLiteral":case"Literal":if(typeof r.value=="string"&&s.type==="ExpressionStatement"&&typeof s.directive!="string"){let c=e.grandparent;return c.type==="Program"||c.type==="BlockStatement"}return n==="object"&&G(s)&&ye(r);case"AssignmentExpression":return!((n==="init"||n==="update")&&s.type==="ForStatement"||n==="expression"&&r.left.type!=="ObjectPattern"&&s.type==="ExpressionStatement"||n==="key"&&s.type==="TSPropertySignature"||s.type==="AssignmentExpression"||n==="expressions"&&s.type==="SequenceExpression"&&e.match(void 0,void 0,(c,A)=>(A==="init"||A==="update")&&c.type==="ForStatement")||n==="value"&&s.type==="Property"&&e.match(void 0,void 0,(c,A)=>A==="properties"&&c.type==="ObjectPattern")||s.type==="NGChainedExpression"||n==="node"&&s.type==="JsExpressionRoot");case"ConditionalExpression":switch(s.type){case"TaggedTemplateExpression":case"UnaryExpression":case"SpreadElement":case"BinaryExpression":case"LogicalExpression":case"NGPipeExpression":case"ExportDefaultDeclaration":case"AwaitExpression":case"JSXSpreadAttribute":case"TSTypeAssertion":case"TypeCastExpression":case"TSAsExpression":case"TSSatisfiesExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":case"TSNonNullExpression":return!0;case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"ConditionalExpression":return t.experimentalTernaries?!1:n==="test";case"MemberExpression":case"OptionalMemberExpression":return n==="object";default:return!1}case"FunctionExpression":switch(s.type){case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"TaggedTemplateExpression":return!0;default:return!1}case"ArrowFunctionExpression":switch(s.type){case"BinaryExpression":return s.operator!=="|>"||((y=r.extra)==null?void 0:y.parenthesized);case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"MemberExpression":case"OptionalMemberExpression":return n==="object";case"TSAsExpression":case"TSSatisfiesExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":case"TSNonNullExpression":case"BindExpression":case"TaggedTemplateExpression":case"UnaryExpression":case"LogicalExpression":case"AwaitExpression":case"TSTypeAssertion":return!0;case"ConditionalExpression":return n==="test";default:return!1}case"ClassExpression":switch(s.type){case"NewExpression":return n==="callee";default:return!1}case"OptionalMemberExpression":case"OptionalCallExpression":case"CallExpression":case"MemberExpression":if(Cc(e))return!0;case"TaggedTemplateExpression":case"TSNonNullExpression":if(n==="callee"&&(s.type==="BindExpression"||s.type==="NewExpression")){let c=r;for(;c;)switch(c.type){case"CallExpression":case"OptionalCallExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":case"BindExpression":c=c.object;break;case"TaggedTemplateExpression":c=c.tag;break;case"TSNonNullExpression":c=c.expression;break;default:return!1}}return!1;case"BindExpression":return n==="callee"&&(s.type==="BindExpression"||s.type==="NewExpression")||n==="object"&&G(s);case"NGPipeExpression":return!(s.type==="NGRoot"||s.type==="NGMicrosyntaxExpression"||s.type==="ObjectProperty"&&!((C=r.extra)!=null&&C.parenthesized)||U(s)||n==="arguments"&&w(s)||n==="right"&&s.type==="NGPipeExpression"||n==="property"&&s.type==="MemberExpression"||s.type==="AssignmentExpression");case"JSXFragment":case"JSXElement":return n==="callee"||n==="left"&&s.type==="BinaryExpression"&&s.operator==="<"||!U(s)&&s.type!=="ArrowFunctionExpression"&&s.type!=="AssignmentExpression"&&s.type!=="AssignmentPattern"&&s.type!=="BinaryExpression"&&s.type!=="NewExpression"&&s.type!=="ConditionalExpression"&&s.type!=="ExpressionStatement"&&s.type!=="JsExpressionRoot"&&s.type!=="JSXAttribute"&&s.type!=="JSXElement"&&s.type!=="JSXExpressionContainer"&&s.type!=="JSXFragment"&&s.type!=="LogicalExpression"&&!w(s)&&!Te(s)&&s.type!=="ReturnStatement"&&s.type!=="ThrowStatement"&&s.type!=="TypeCastExpression"&&s.type!=="VariableDeclarator"&&s.type!=="YieldExpression";case"TSInstantiationExpression":return n==="object"&&G(s)}return!1}var fc=R(["BlockStatement","BreakStatement","ComponentDeclaration","ClassBody","ClassDeclaration","ClassMethod","ClassProperty","PropertyDefinition","ClassPrivateProperty","ContinueStatement","DebuggerStatement","DeclareComponent","DeclareClass","DeclareExportAllDeclaration","DeclareExportDeclaration","DeclareFunction","DeclareHook","DeclareInterface","DeclareModule","DeclareModuleExports","DeclareNamespace","DeclareVariable","DeclareEnum","DoWhileStatement","EnumDeclaration","ExportAllDeclaration","ExportDefaultDeclaration","ExportNamedDeclaration","ExpressionStatement","ForInStatement","ForOfStatement","ForStatement","FunctionDeclaration","HookDeclaration","IfStatement","ImportDeclaration","InterfaceDeclaration","LabeledStatement","MethodDefinition","ReturnStatement","SwitchStatement","ThrowStatement","TryStatement","TSDeclareFunction","TSEnumDeclaration","TSImportEqualsDeclaration","TSInterfaceDeclaration","TSModuleDeclaration","TSNamespaceExportDeclaration","TypeAlias","VariableDeclaration","WhileStatement","WithStatement"]);function Dc(e){let t=0,{node:r}=e;for(;r;){let n=e.getParentNode(t++);if((n==null?void 0:n.type)==="ForStatement"&&n.init===r)return!0;r=n}return!1}function Ec(e){return ur(e,t=>t.type==="ObjectTypeAnnotation"&&ur(t,r=>r.type==="FunctionTypeAnnotation"))}function Fc(e){return ue(e)}function yr(e){let{parent:t,key:r}=e;switch(t.type){case"NGPipeExpression":if(r==="arguments"&&e.isLast)return e.callParent(yr);break;case"ObjectProperty":if(r==="value")return e.callParent(()=>e.key==="properties"&&e.isLast);break;case"BinaryExpression":case"LogicalExpression":if(r==="right")return e.callParent(yr);break;case"ConditionalExpression":if(r==="alternate")return e.callParent(yr);break;case"UnaryExpression":if(t.prefix)return e.callParent(yr);break}return!1}function xi(e,t){let{node:r,parent:n}=e;return r.type==="FunctionExpression"||r.type==="ClassExpression"?n.type==="ExportDefaultDeclaration"||!ls(e,t):!Jt(r)||n.type!=="ExportDefaultDeclaration"&&ls(e,t)?!1:e.call(()=>xi(e,t),...Lr(r))}function Cc(e){return!!(e.match(void 0,(t,r)=>r==="expression"&&t.type==="ChainExpression",(t,r)=>r==="tag"&&t.type==="TaggedTemplateExpression")||e.match(t=>t.type==="OptionalCallExpression"||t.type==="OptionalMemberExpression",(t,r)=>r==="tag"&&t.type==="TaggedTemplateExpression")||e.match(t=>t.type==="OptionalCallExpression"||t.type==="OptionalMemberExpression",(t,r)=>r==="expression"&&t.type==="TSNonNullExpression",(t,r)=>r==="tag"&&t.type==="TaggedTemplateExpression")||e.match(void 0,(t,r)=>r==="expression"&&t.type==="ChainExpression",(t,r)=>r==="expression"&&t.type==="TSNonNullExpression",(t,r)=>r==="tag"&&t.type==="TaggedTemplateExpression")||e.match(void 0,(t,r)=>r==="expression"&&t.type==="TSNonNullExpression",(t,r)=>r==="expression"&&t.type==="ChainExpression",(t,r)=>r==="tag"&&t.type==="TaggedTemplateExpression")||e.match(t=>t.type==="OptionalMemberExpression"||t.type==="OptionalCallExpression",(t,r)=>r==="object"&&t.type==="MemberExpression"||r==="callee"&&(t.type==="CallExpression"||t.type==="NewExpression"))||e.match(t=>t.type==="OptionalMemberExpression"||t.type==="OptionalCallExpression",(t,r)=>r==="expression"&&t.type==="TSNonNullExpression",(t,r)=>r==="object"&&t.type==="MemberExpression"||r==="callee"&&t.type==="CallExpression")||e.match(t=>t.type==="CallExpression"||t.type==="MemberExpression",(t,r)=>r==="expression"&&t.type==="ChainExpression")&&(e.match(void 0,void 0,(t,r)=>r==="callee"&&(t.type==="CallExpression"&&!t.optional||t.type==="NewExpression")||r==="object"&&t.type==="MemberExpression"&&!t.optional)||e.match(void 0,void 0,(t,r)=>r==="expression"&&t.type==="TSNonNullExpression",(t,r)=>r==="object"&&t.type==="MemberExpression"||r==="callee"&&t.type==="CallExpression"))||e.match(t=>t.type==="CallExpression"||t.type==="MemberExpression",(t,r)=>r==="expression"&&t.type==="TSNonNullExpression",(t,r)=>r==="expression"&&t.type==="ChainExpression",(t,r)=>r==="object"&&t.type==="MemberExpression"||r==="callee"&&t.type==="CallExpression"))}function ms(e){return e.type==="Identifier"?!0:G(e)?!e.computed&&!e.optional&&e.property.type==="Identifier"&&ms(e.object):!1}function Ac(e){return e.type==="ChainExpression"&&(e=e.expression),ms(e)||w(e)&&!e.optional&&ms(e.callee)}var Le=ls;function dc(e,t){let r=t-1;r=Ye(e,r,{backwards:!0}),r=He(e,r,{backwards:!0}),r=Ye(e,r,{backwards:!0});let n=He(e,r,{backwards:!0});return r!==n}var hi=dc;var Tc=()=>!0;function ys(e,t){let r=e.node;return r.printed=!0,t.printer.printComment(e,t)}function xc(e,t){var m;let r=e.node,n=[ys(e,t)],{printer:s,originalText:u,locStart:i,locEnd:a}=t;if((m=s.isBlockComment)==null?void 0:m.call(s,r)){let f=ee(u,a(r))?ee(u,i(r),{backwards:!0})?F:x:" ";n.push(f)}else n.push(F);let p=He(u,Ye(u,a(r)));return p!==!1&&ee(u,p)&&n.push(F),n}function hc(e,t,r){var p;let n=e.node,s=ys(e,t),{printer:u,originalText:i,locStart:a}=t,o=(p=u.isBlockComment)==null?void 0:p.call(u,n);if(r!=null&&r.hasLineSuffix&&!(r!=null&&r.isBlock)||ee(i,a(n),{backwards:!0})){let m=hi(i,a(n));return{doc:Xn([F,m?F:"",s]),isBlock:o,hasLineSuffix:!0}}return!o||r!=null&&r.hasLineSuffix?{doc:[Xn([" ",s]),Ce],isBlock:o,hasLineSuffix:!0}:{doc:[" ",s],isBlock:o,hasLineSuffix:!1}}function N(e,t,r={}){let{node:n}=e;if(!O(n==null?void 0:n.comments))return"";let{indent:s=!1,marker:u,filter:i=Tc}=r,a=[];if(e.each(({node:p})=>{p.leading||p.trailing||p.marker!==u||!i(p)||a.push(ys(e,t))},"comments"),a.length===0)return"";let o=b(F,a);return s?D([F,o]):o}function fs(e,t){let r=e.node;if(!r)return{};let n=t[Symbol.for("printedComments")];if((r.comments||[]).filter(o=>!n.has(o)).length===0)return{leading:"",trailing:""};let u=[],i=[],a;return e.each(()=>{let o=e.node;if(n!=null&&n.has(o))return;let{leading:p,trailing:m}=o;p?u.push(xc(e,t)):m&&(a=hc(e,t,a),i.push(a.doc))},"comments"),{leading:u,trailing:i}}function Ee(e,t,r){let{leading:n,trailing:s}=fs(e,r);return!n&&!s?t:lr(t,u=>[n,u,s])}var Ds=class extends Error{name="UnexpectedNodeError";constructor(t,r,n="type"){super(`Unexpected ${r} node ${n}: ${JSON.stringify(t[n])}.`),this.node=t}},qe=Ds;function Es(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}var We,Fs=class{constructor(t){Ys(this,We);Hs(this,We,new Set(t))}getLeadingWhitespaceCount(t){let r=mt(this,We),n=0;for(let s=0;s<t.length&&r.has(t.charAt(s));s++)n++;return n}getTrailingWhitespaceCount(t){let r=mt(this,We),n=0;for(let s=t.length-1;s>=0&&r.has(t.charAt(s));s--)n++;return n}getLeadingWhitespace(t){let r=this.getLeadingWhitespaceCount(t);return t.slice(0,r)}getTrailingWhitespace(t){let r=this.getTrailingWhitespaceCount(t);return t.slice(t.length-r)}hasLeadingWhitespace(t){return mt(this,We).has(t.charAt(0))}hasTrailingWhitespace(t){return mt(this,We).has(v(!1,t,-1))}trimStart(t){let r=this.getLeadingWhitespaceCount(t);return t.slice(r)}trimEnd(t){let r=this.getTrailingWhitespaceCount(t);return t.slice(0,t.length-r)}trim(t){return this.trimEnd(this.trimStart(t))}split(t,r=!1){let n=`[${Es([...mt(this,We)].join(""))}]+`,s=new RegExp(r?`(${n})`:n,"u");return t.split(s)}hasWhitespaceCharacter(t){let r=mt(this,We);return Array.prototype.some.call(t,n=>r.has(n))}hasNonWhitespaceCharacter(t){let r=mt(this,We);return Array.prototype.some.call(t,n=>!r.has(n))}isWhitespaceOnly(t){let r=mt(this,We);return Array.prototype.every.call(t,n=>r.has(n))}};We=new WeakMap;var gi=Fs;var Hr=new gi(` 
\r	`),Cs=e=>e===""||e===x||e===F||e===E;function gc(e,t,r){var _,M,J,I,q;let{node:n}=e;if(n.type==="JSXElement"&&Jc(n))return[r("openingElement"),r("closingElement")];let s=n.type==="JSXElement"?r("openingElement"):r("openingFragment"),u=n.type==="JSXElement"?r("closingElement"):r("closingFragment");if(n.children.length===1&&n.children[0].type==="JSXExpressionContainer"&&(n.children[0].expression.type==="TemplateLiteral"||n.children[0].expression.type==="TaggedTemplateExpression"))return[s,...e.map(r,"children"),u];n.children=n.children.map(k=>Nc(k)?{type:"JSXText",value:" ",raw:" "}:k);let i=n.children.some(Y),a=n.children.filter(k=>k.type==="JSXExpressionContainer").length>1,o=n.type==="JSXElement"&&n.openingElement.attributes.length>1,p=re(s)||i||o||a,m=e.parent.rootMarker==="mdx",f=t.singleQuote?"{' '}":'{" "}',y=m?x:S([f,E]," "),C=((M=(_=n.openingElement)==null?void 0:_.name)==null?void 0:M.name)==="fbt",c=Sc(e,t,r,y,C),A=n.children.some(k=>fr(k));for(let k=c.length-2;k>=0;k--){let W=c[k]===""&&c[k+1]==="",ne=c[k]===F&&c[k+1]===""&&c[k+2]===F,Z=(c[k]===E||c[k]===F)&&c[k+1]===""&&c[k+2]===y,lt=c[k]===y&&c[k+1]===""&&(c[k+2]===E||c[k+2]===F),L=c[k]===y&&c[k+1]===""&&c[k+2]===y,se=c[k]===E&&c[k+1]===""&&c[k+2]===F||c[k]===F&&c[k+1]===""&&c[k+2]===E;ne&&A||W||Z||L||se?c.splice(k,2):lt&&c.splice(k+1,2)}for(;c.length>0&&Cs(v(!1,c,-1));)c.pop();for(;c.length>1&&Cs(c[0])&&Cs(c[1]);)c.shift(),c.shift();let T=[""];for(let[k,W]of c.entries()){if(W===y){if(k===1&&wu(c[k-1])){if(c.length===2){T.push([T.pop(),f]);continue}T.push([f,F],"");continue}else if(k===c.length-1){T.push([T.pop(),f]);continue}else if(c[k-1]===""&&c[k-2]===F){T.push([T.pop(),f]);continue}}k%2===0?T.push([T.pop(),W]):T.push(W,""),re(W)&&(p=!0)}let B=A?Nr(T):l(T,{shouldBreak:!0});if(((J=t.cursorNode)==null?void 0:J.type)==="JSXText"&&n.children.includes(t.cursorNode)?B=[mr,B,mr]:((I=t.nodeBeforeCursor)==null?void 0:I.type)==="JSXText"&&n.children.includes(t.nodeBeforeCursor)?B=[mr,B]:((q=t.nodeAfterCursor)==null?void 0:q.type)==="JSXText"&&n.children.includes(t.nodeAfterCursor)&&(B=[B,mr]),m)return B;let g=l([s,D([F,B]),F,u]);return p?g:tt([l([s,...c,u]),g])}function Sc(e,t,r,n,s){let u="",i=[u];function a(p){u=p,i.push([i.pop(),p])}function o(p){p!==""&&(u=p,i.push(p,""))}return e.each(({node:p,next:m})=>{if(p.type==="JSXText"){let f=ae(p);if(fr(p)){let y=Hr.split(f,!0);y[0]===""&&(y.shift(),/\n/u.test(y[0])?o(Bi(s,y[1],p,m)):o(n),y.shift());let C;if(v(!1,y,-1)===""&&(y.pop(),C=y.pop()),y.length===0)return;for(let[c,A]of y.entries())c%2===1?o(x):a(A);C!==void 0?/\n/u.test(C)?o(Bi(s,u,p,m)):o(n):o(Si(s,u,p,m))}else/\n/u.test(f)?f.match(/\n/gu).length>1&&o(F):o(n)}else{let f=r();if(a(f),m&&fr(m)){let C=Hr.trim(ae(m)),[c]=Hr.split(C);o(Si(s,c,p,m))}else o(F)}},"children"),i}function Si(e,t,r,n){return e?"":r.type==="JSXElement"&&!r.closingElement||(n==null?void 0:n.type)==="JSXElement"&&!n.closingElement?t.length===1?E:F:E}function Bi(e,t,r,n){return e?F:t.length===1?r.type==="JSXElement"&&!r.closingElement||(n==null?void 0:n.type)==="JSXElement"&&!n.closingElement?F:E:F}var Bc=new Set(["ArrayExpression","JSXAttribute","JSXElement","JSXExpressionContainer","JSXFragment","ExpressionStatement","CallExpression","OptionalCallExpression","ConditionalExpression","JsExpressionRoot"]);function bc(e,t,r){let{parent:n}=e;if(Bc.has(n.type))return t;let s=Pc(e),u=Le(e,r);return l([u?"":S("("),D([E,t]),E,u?"":S(")")],{shouldBreak:s})}function Pc(e){return e.match(void 0,t=>t.type==="ArrowFunctionExpression",w)&&(e.match(void 0,void 0,void 0,t=>t.type==="JSXExpressionContainer")||e.match(void 0,void 0,void 0,t=>t.type==="ChainExpression",t=>t.type==="JSXExpressionContainer"))}function kc(e,t,r){let{node:n}=e,s=[];if(s.push(r("name")),n.value){let u;if(K(n.value)){let i=ae(n.value),a=X(!1,X(!1,i.slice(1,-1),"&apos;","'"),"&quot;",'"'),o=Sr(a,t.jsxSingleQuote);a=o==='"'?X(!1,a,'"',"&quot;"):X(!1,a,"'","&apos;"),u=e.call(()=>Ee(e,ve(o+a+o),t),"value")}else u=r("value");s.push("=",u)}return s}function Ic(e,t,r){let{node:n}=e,s=(u,i)=>u.type==="JSXEmptyExpression"||!d(u)&&(U(u)||ue(u)||u.type==="ArrowFunctionExpression"||u.type==="AwaitExpression"&&(s(u.argument,u)||u.argument.type==="JSXElement")||w(u)||u.type==="ChainExpression"&&w(u.expression)||u.type==="FunctionExpression"||u.type==="TemplateLiteral"||u.type==="TaggedTemplateExpression"||u.type==="DoExpression"||Y(i)&&(u.type==="ConditionalExpression"||Fe(u)));return s(n.expression,e.parent)?l(["{",r("expression"),je,"}"]):l(["{",D([E,r("expression")]),E,je,"}"])}function Lc(e,t,r){var a,o;let{node:n}=e,s=d(n.name)||d(n.typeParameters)||d(n.typeArguments);if(n.selfClosing&&n.attributes.length===0&&!s)return["<",r("name"),n.typeArguments?r("typeArguments"):r("typeParameters")," />"];if(((a=n.attributes)==null?void 0:a.length)===1&&K(n.attributes[0].value)&&!n.attributes[0].value.value.includes(`
`)&&!s&&!d(n.attributes[0]))return l(["<",r("name"),n.typeArguments?r("typeArguments"):r("typeParameters")," ",...e.map(r,"attributes"),n.selfClosing?" />":">"]);let u=(o=n.attributes)==null?void 0:o.some(p=>K(p.value)&&p.value.value.includes(`
`)),i=t.singleAttributePerLine&&n.attributes.length>1?F:x;return l(["<",r("name"),n.typeArguments?r("typeArguments"):r("typeParameters"),D(e.map(()=>[i,r()],"attributes")),...wc(n,t,s)],{shouldBreak:u})}function wc(e,t,r){return e.selfClosing?[x,"/>"]:Oc(e,t,r)?[">"]:[E,">"]}function Oc(e,t,r){let n=e.attributes.length>0&&d(v(!1,e.attributes,-1),h.Trailing);return e.attributes.length===0&&!r||(t.bracketSameLine||t.jsxBracketSameLine)&&(!r||e.attributes.length>0)&&!n}function _c(e,t,r){let{node:n}=e,s=[];s.push("</");let u=r("name");return d(n.name,h.Leading|h.Line)?s.push(D([F,u]),F):d(n.name,h.Leading|h.Block)?s.push(" ",u):s.push(u),s.push(">"),s}function Mc(e,t){let{node:r}=e,n=d(r),s=d(r,h.Line),u=r.type==="JSXOpeningFragment";return[u?"<":"</",D([s?F:n&&!u?" ":"",N(e,t)]),s?F:"",">"]}function vc(e,t,r){let n=Ee(e,gc(e,t,r),t);return bc(e,n,t)}function jc(e,t){let{node:r}=e,n=d(r,h.Line);return[N(e,t,{indent:n}),n?F:""]}function Rc(e,t,r){let{node:n}=e;return["{",e.call(({node:s})=>{let u=["...",r()];return!d(s)||!rs(e)?u:[D([E,Ee(e,u,t)]),E]},n.type==="JSXSpreadAttribute"?"argument":"expression"),"}"]}function bi(e,t,r){let{node:n}=e;if(n.type.startsWith("JSX"))switch(n.type){case"JSXAttribute":return kc(e,t,r);case"JSXIdentifier":return n.name;case"JSXNamespacedName":return b(":",[r("namespace"),r("name")]);case"JSXMemberExpression":return b(".",[r("object"),r("property")]);case"JSXSpreadAttribute":case"JSXSpreadChild":return Rc(e,t,r);case"JSXExpressionContainer":return Ic(e,t,r);case"JSXFragment":case"JSXElement":return vc(e,t,r);case"JSXOpeningElement":return Lc(e,t,r);case"JSXClosingElement":return _c(e,t,r);case"JSXOpeningFragment":case"JSXClosingFragment":return Mc(e,t);case"JSXEmptyExpression":return jc(e,t);case"JSXText":throw new Error("JSXText should be handled by JSXElement");default:throw new qe(n,"JSX")}}function Jc(e){if(e.children.length===0)return!0;if(e.children.length>1)return!1;let t=e.children[0];return t.type==="JSXText"&&!fr(t)}function fr(e){return e.type==="JSXText"&&(Hr.hasNonWhitespaceCharacter(ae(e))||!/\n/u.test(ae(e)))}function Nc(e){return e.type==="JSXExpressionContainer"&&K(e.expression)&&e.expression.value===" "&&!d(e.expression)}function Pi(e){let{node:t,parent:r}=e;if(!Y(t)||!Y(r))return!1;let{index:n,siblings:s}=e,u;for(let i=n;i>0;i--){let a=s[i-1];if(!(a.type==="JSXText"&&!fr(a))){u=a;break}}return(u==null?void 0:u.type)==="JSXExpressionContainer"&&u.expression.type==="JSXEmptyExpression"&&kt(u.expression)}function Gc(e){return kt(e.node)||Pi(e)}var Vr=Gc;var qc=0;function $r(e,t,r){var M;let{node:n,parent:s,grandparent:u,key:i}=e,a=i!=="body"&&(s.type==="IfStatement"||s.type==="WhileStatement"||s.type==="SwitchStatement"||s.type==="DoWhileStatement"),o=n.operator==="|>"&&((M=e.root.extra)==null?void 0:M.__isUsingHackPipeline),p=As(e,t,r,!1,a);if(a)return p;if(o)return l(p);if(w(s)&&s.callee===n||s.type==="UnaryExpression"||G(s)&&!s.computed)return l([D([E,...p]),E]);let m=s.type==="ReturnStatement"||s.type==="ThrowStatement"||s.type==="JSXExpressionContainer"&&u.type==="JSXAttribute"||n.operator!=="|"&&s.type==="JsExpressionRoot"||n.type!=="NGPipeExpression"&&(s.type==="NGRoot"&&t.parser==="__ng_binding"||s.type==="NGMicrosyntaxExpression"&&u.type==="NGMicrosyntax"&&u.body.length===1)||n===s.body&&s.type==="ArrowFunctionExpression"||n!==s.body&&s.type==="ForStatement"||s.type==="ConditionalExpression"&&u.type!=="ReturnStatement"&&u.type!=="ThrowStatement"&&!w(u)||s.type==="TemplateLiteral",f=s.type==="AssignmentExpression"||s.type==="VariableDeclarator"||s.type==="ClassProperty"||s.type==="PropertyDefinition"||s.type==="TSAbstractPropertyDefinition"||s.type==="ClassPrivateProperty"||Te(s),y=Fe(n.left)&&ar(n.operator,n.left.operator);if(m||Ht(n)&&!y||!Ht(n)&&f)return l(p);if(p.length===0)return"";let C=Y(n.right),c=p.findIndex(J=>typeof J!="string"&&!Array.isArray(J)&&J.type===fe),A=p.slice(0,c===-1?1:c+1),T=p.slice(A.length,C?-1:void 0),B=Symbol("logicalChain-"+ ++qc),g=l([...A,D(T)],{id:B});if(!C)return g;let _=v(!1,p,-1);return l([g,xt(_,{groupId:B})])}function As(e,t,r,n,s){var B;let{node:u}=e;if(!Fe(u))return[l(r())];let i=[];ar(u.operator,u.left.operator)?i=e.call(g=>As(g,t,r,!0,s),"left"):i.push(l(r("left")));let a=Ht(u),o=(u.operator==="|>"||u.type==="NGPipeExpression"||Wc(e,t))&&!de(t.originalText,u.right),m=!d(u.right,h.Leading,Gr)&&de(t.originalText,u.right),f=u.type==="NGPipeExpression"?"|":u.operator,y=u.type==="NGPipeExpression"&&u.arguments.length>0?l(D([E,": ",b([x,": "],e.map(()=>be(2,l(r())),"arguments"))])):"",C;if(a)C=[f,de(t.originalText,u.right)?D([x,r("right"),y]):[" ",r("right"),y]];else{let _=f==="|>"&&((B=e.root.extra)==null?void 0:B.__isUsingHackPipeline)?e.call(M=>As(M,t,r,!0,s),"right"):r("right");if(t.experimentalOperatorPosition==="start"){let M="";if(m)switch(Be(_)){case ge:M=_.splice(0,1)[0];break;case Se:M=_.contents.splice(0,1)[0];break}C=[x,M,f," ",_,y]}else C=[o?x:"",f,o?" ":x,_,y]}let{parent:c}=e,A=d(u.left,h.Trailing|h.Line);if((A||!(s&&u.type==="LogicalExpression")&&c.type!==u.type&&u.left.type!==u.type&&u.right.type!==u.type)&&(C=l(C,{shouldBreak:A})),t.experimentalOperatorPosition==="start"?i.push(a||m?" ":"",C):i.push(o?"":" ",C),n&&d(u)){let g=Ut(Ee(e,i,t));return g.type===_e?g.parts:Array.isArray(g)?g:[g]}return i}function Ht(e){return e.type!=="LogicalExpression"?!1:!!(ue(e.right)&&e.right.properties.length>0||U(e.right)&&e.right.elements.length>0||Y(e.right))}var ki=e=>e.type==="BinaryExpression"&&e.operator==="|";function Wc(e,t){return(t.parser==="__vue_expression"||t.parser==="__vue_ts_expression")&&ki(e.node)&&!e.hasAncestor(r=>!ki(r)&&r.type!=="JsExpressionRoot")}function Li(e,t,r){let{node:n}=e;if(n.type.startsWith("NG"))switch(n.type){case"NGRoot":return[r("node"),d(n.node)?" //"+Ve(n.node)[0].value.trimEnd():""];case"NGPipeExpression":return $r(e,t,r);case"NGChainedExpression":return l(b([";",x],e.map(()=>Yc(e)?r():["(",r(),")"],"expressions")));case"NGEmptyExpression":return"";case"NGMicrosyntax":return e.map(()=>[e.isFirst?"":Ii(e)?" ":[";",x],r()],"body");case"NGMicrosyntaxKey":return/^[$_a-z][\w$]*(?:-[$_a-z][\w$])*$/iu.test(n.name)?n.name:JSON.stringify(n.name);case"NGMicrosyntaxExpression":return[r("expression"),n.alias===null?"":[" as ",r("alias")]];case"NGMicrosyntaxKeyedExpression":{let{index:s,parent:u}=e,i=Ii(e)||Uc(e)||(s===1&&(n.key.name==="then"||n.key.name==="else"||n.key.name==="as")||s===2&&(n.key.name==="else"&&u.body[s-1].type==="NGMicrosyntaxKeyedExpression"&&u.body[s-1].key.name==="then"||n.key.name==="track"))&&u.body[0].type==="NGMicrosyntaxExpression";return[r("key"),i?" ":": ",r("expression")]}case"NGMicrosyntaxLet":return["let ",r("key"),n.value===null?"":[" = ",r("value")]];case"NGMicrosyntaxAs":return[r("key")," as ",r("alias")];default:throw new qe(n,"Angular")}}function Ii({node:e,index:t}){return e.type==="NGMicrosyntaxKeyedExpression"&&e.key.name==="of"&&t===1}function Uc(e){let{node:t}=e;return e.parent.body[1].key.name==="of"&&t.type==="NGMicrosyntaxKeyedExpression"&&t.key.name==="track"&&t.key.type==="NGMicrosyntaxKey"}var Xc=R(["CallExpression","OptionalCallExpression","AssignmentExpression"]);function Yc({node:e}){return ur(e,Xc)}function ds(e,t,r){let{node:n}=e;return l([b(x,e.map(r,"decorators")),_i(n,t)?F:x])}function wi(e,t,r){return Mi(e.node)?[b(F,e.map(r,"declaration","decorators")),F]:""}function Oi(e,t,r){let{node:n,parent:s}=e,{decorators:u}=n;if(!O(u)||Mi(s)||Vr(e))return"";let i=n.type==="ClassExpression"||n.type==="ClassDeclaration"||_i(n,t);return[e.key==="declaration"&&yu(s)?F:i?Ce:"",b(x,e.map(r,"decorators")),x]}function _i(e,t){return e.decorators.some(r=>ee(t.originalText,P(r)))}function Mi(e){var r;if(e.type!=="ExportDefaultDeclaration"&&e.type!=="ExportNamedDeclaration"&&e.type!=="DeclareExportDeclaration")return!1;let t=(r=e.declaration)==null?void 0:r.decorators;return O(t)&&Bt(e,t[0])}var Dt=class extends Error{name="ArgExpansionBailout"};function Hc(e,t,r){let{node:n}=e,s=le(n);if(s.length===0)return["(",N(e,t),")"];let u=s.length-1;if(Kc(s)){let f=["("];return Gt(e,(y,C)=>{f.push(r()),C!==u&&f.push(", ")}),f.push(")"),f}let i=!1,a=[];Gt(e,({node:f},y)=>{let C=r();y===u||(me(f,t)?(i=!0,C=[C,",",F,F]):C=[C,",",x]),a.push(C)});let o=!t.parser.startsWith("__ng_")&&n.type!=="ImportExpression"&&n.type!=="TSImportType"&&ce(t,"all")?",":"";if(n.type==="TSImportType"&&s.length===1&&(s[0].type==="TSLiteralType"&&K(s[0].literal)||K(s[0]))&&!d(s[0]))return l(["(",...a,S(o),")"]);function p(){return l(["(",D([x,...a]),o,x,")"],{shouldBreak:!0})}if(i||e.parent.type!=="Decorator"&&Au(s))return p();if($c(s)){let f=a.slice(1);if(f.some(re))return p();let y;try{y=r(Gn(n,0),{expandFirstArg:!0})}catch(C){if(C instanceof Dt)return p();throw C}return re(y)?[Ce,tt([["(",l(y,{shouldBreak:!0}),", ",...f,")"],p()])]:tt([["(",y,", ",...f,")"],["(",l(y,{shouldBreak:!0}),", ",...f,")"],p()])}if(Vc(s,a,t)){let f=a.slice(0,-1);if(f.some(re))return p();let y;try{y=r(Gn(n,-1),{expandLastArg:!0})}catch(C){if(C instanceof Dt)return p();throw C}return re(y)?[Ce,tt([["(",...f,l(y,{shouldBreak:!0}),")"],p()])]:tt([["(",...f,y,")"],["(",...f,l(y,{shouldBreak:!0}),")"],p()])}let m=["(",D([E,...a]),S(o),E,")"];return vr(e)?m:l(m,{shouldBreak:a.some(re)||i})}function Dr(e,t=!1){return ue(e)&&(e.properties.length>0||d(e))||U(e)&&(e.elements.length>0||d(e))||e.type==="TSTypeAssertion"&&Dr(e.expression)||xe(e)&&Dr(e.expression)||e.type==="FunctionExpression"||e.type==="ArrowFunctionExpression"&&(!e.returnType||!e.returnType.typeAnnotation||e.returnType.typeAnnotation.type!=="TSTypeReference"||Qc(e.body))&&(e.body.type==="BlockStatement"||e.body.type==="ArrowFunctionExpression"&&Dr(e.body,!0)||ue(e.body)||U(e.body)||!t&&(w(e.body)||e.body.type==="ConditionalExpression")||Y(e.body))||e.type==="DoExpression"||e.type==="ModuleExpression"}function Vc(e,t,r){var u,i;let n=v(!1,e,-1);if(e.length===1){let a=v(!1,t,-1);if((u=a.label)!=null&&u.embed&&((i=a.label)==null?void 0:i.hug)!==!1)return!0}let s=v(!1,e,-2);return!d(n,h.Leading)&&!d(n,h.Trailing)&&Dr(n)&&(!s||s.type!==n.type)&&(e.length!==2||s.type!=="ArrowFunctionExpression"||!U(n))&&!(e.length>1&&Ts(n,r))}function $c(e){if(e.length!==2)return!1;let[t,r]=e;return t.type==="ModuleExpression"&&zc(r)?!0:!d(t)&&(t.type==="FunctionExpression"||t.type==="ArrowFunctionExpression"&&t.body.type==="BlockStatement")&&r.type!=="FunctionExpression"&&r.type!=="ArrowFunctionExpression"&&r.type!=="ConditionalExpression"&&ji(r)&&!Dr(r)}function ji(e){if(e.type==="ParenthesizedExpression")return ji(e.expression);if(xe(e)||e.type==="TypeCastExpression"){let{typeAnnotation:t}=e;if(t.type==="TypeAnnotation"&&(t=t.typeAnnotation),t.type==="TSArrayType"&&(t=t.elementType,t.type==="TSArrayType"&&(t=t.elementType)),t.type==="GenericTypeAnnotation"||t.type==="TSTypeReference"){let r=t.typeArguments??t.typeParameters;(r==null?void 0:r.params.length)===1&&(t=r.params[0])}return Nt(t)&&we(e.expression,1)}return yt(e)&&le(e).length>1?!1:Fe(e)?we(e.left,1)&&we(e.right,1):Jn(e)||we(e)}function Kc(e){return e.length===2?vi(e,0):e.length===3?e[0].type==="Identifier"&&vi(e,1):!1}function vi(e,t){let r=e[t],n=e[t+1];return r.type==="ArrowFunctionExpression"&&Q(r).length===0&&r.body.type==="BlockStatement"&&n.type==="ArrayExpression"&&!e.some(s=>d(s))}function Qc(e){return e.type==="BlockStatement"&&(e.body.some(t=>t.type!=="EmptyStatement")||d(e,h.Dangling))}function zc(e){if(!(e.type==="ObjectExpression"&&e.properties.length===1))return!1;let[t]=e.properties;return Te(t)?!t.computed&&(t.key.type==="Identifier"&&t.key.name==="type"||K(t.key)&&t.key.value==="type")&&K(t.value)&&t.value.value==="module":!1}var Lt=Hc;var Zc=e=>((e.type==="ChainExpression"||e.type==="TSNonNullExpression")&&(e=e.expression),w(e)&&le(e).length>0);function Ri(e,t,r){var p;let n=r("object"),s=xs(e,t,r),{node:u}=e,i=e.findAncestor(m=>!(G(m)||m.type==="TSNonNullExpression")),a=e.findAncestor(m=>!(m.type==="ChainExpression"||m.type==="TSNonNullExpression")),o=i&&(i.type==="NewExpression"||i.type==="BindExpression"||i.type==="AssignmentExpression"&&i.left.type!=="Identifier")||u.computed||u.object.type==="Identifier"&&u.property.type==="Identifier"&&!G(a)||(a.type==="AssignmentExpression"||a.type==="VariableDeclarator")&&(Zc(u.object)||((p=n.label)==null?void 0:p.memberChain));return at(n.label,[n,o?s:l(D([E,s]))])}function xs(e,t,r){let n=r("property"),{node:s}=e,u=V(e);return s.computed?!s.property||ye(s.property)?[u,"[",n,"]"]:l([u,"[",D([E,n]),E,"]"]):[u,".",n]}function Ji(e,t,r){if(e.node.type==="ChainExpression")return e.call(()=>Ji(e,t,r),"expression");let n=(e.parent.type==="ChainExpression"?e.grandparent:e.parent).type==="ExpressionStatement",s=[];function u(L){let{originalText:se}=t,Ae=ot(se,P(L));return se.charAt(Ae)===")"?Ae!==!1&&vt(se,Ae+1):me(L,t)}function i(){let{node:L}=e;if(L.type==="ChainExpression")return e.call(i,"expression");if(w(L)&&(dt(L.callee)||w(L.callee))){let se=u(L);s.unshift({node:L,hasTrailingEmptyLine:se,printed:[Ee(e,[V(e),rt(e,t,r),Lt(e,t,r)],t),se?F:""]}),e.call(i,"callee")}else dt(L)?(s.unshift({node:L,needsParens:Le(e,t),printed:Ee(e,G(L)?xs(e,t,r):Kr(e,t,r),t)}),e.call(i,"object")):L.type==="TSNonNullExpression"?(s.unshift({node:L,printed:Ee(e,"!",t)}),e.call(i,"expression")):s.unshift({node:L,printed:r()})}let{node:a}=e;s.unshift({node:a,printed:[V(e),rt(e,t,r),Lt(e,t,r)]}),a.callee&&e.call(i,"callee");let o=[],p=[s[0]],m=1;for(;m<s.length&&(s[m].node.type==="TSNonNullExpression"||w(s[m].node)||G(s[m].node)&&s[m].node.computed&&ye(s[m].node.property));++m)p.push(s[m]);if(!w(s[0].node))for(;m+1<s.length&&(dt(s[m].node)&&dt(s[m+1].node));++m)p.push(s[m]);o.push(p),p=[];let f=!1;for(;m<s.length;++m){if(f&&dt(s[m].node)){if(s[m].node.computed&&ye(s[m].node.property)){p.push(s[m]);continue}o.push(p),p=[],f=!1}(w(s[m].node)||s[m].node.type==="ImportExpression")&&(f=!0),p.push(s[m]),d(s[m].node,h.Trailing)&&(o.push(p),p=[],f=!1)}p.length>0&&o.push(p);function y(L){return/^[A-Z]|^[$_]+$/u.test(L)}function C(L){return L.length<=t.tabWidth}function c(L){var Ot;let se=(Ot=L[1][0])==null?void 0:Ot.node.computed;if(L[0].length===1){let nt=L[0][0].node;return nt.type==="ThisExpression"||nt.type==="Identifier"&&(y(nt.name)||n&&C(nt.name)||se)}let Ae=v(!1,L[0],-1).node;return G(Ae)&&Ae.property.type==="Identifier"&&(y(Ae.property.name)||se)}let A=o.length>=2&&!d(o[1][0].node)&&c(o);function T(L){let se=L.map(Ae=>Ae.printed);return L.length>0&&v(!1,L,-1).needsParens?["(",...se,")"]:se}function B(L){return L.length===0?"":D([F,b(F,L.map(T))])}let g=o.map(T),_=g,M=A?3:2,J=o.flat(),I=J.slice(1,-1).some(L=>d(L.node,h.Leading))||J.slice(0,-1).some(L=>d(L.node,h.Trailing))||o[M]&&d(o[M][0].node,h.Leading);if(o.length<=M&&!I&&!o.some(L=>v(!1,L,-1).hasTrailingEmptyLine))return vr(e)?_:l(_);let q=v(!1,o[A?1:0],-1).node,k=!w(q)&&u(q),W=[T(o[0]),A?o.slice(1,2).map(T):"",k?F:"",B(o.slice(A?2:1))],ne=s.map(({node:L})=>L).filter(w);function Z(){let L=v(!1,v(!1,o,-1),-1).node,se=v(!1,g,-1);return w(L)&&re(se)&&ne.slice(0,-1).some(Ae=>Ae.arguments.some(Rt))}let lt;return I||ne.length>2&&ne.some(L=>!L.arguments.every(se=>we(se)))||g.slice(0,-1).some(re)||Z()?lt=l(W):lt=[re(_)||k?Ce:"",tt([_,W])],at({memberChain:!0},lt)}var Ni=Ji;function Qr(e,t,r){var m;let{node:n}=e,s=n.type==="NewExpression",u=n.type==="ImportExpression",i=V(e),a=le(n),o=a.length===1&&_r(a[0],t.originalText);if(o||el(e)||Pt(n,e.parent)){let f=[];if(Gt(e,()=>{f.push(r())}),!(o&&((m=f[0].label)!=null&&m.embed)))return[s?"new ":"",Gi(e,r),i,rt(e,t,r),"(",b(", ",f),")"]}if(!u&&!s&&dt(n.callee)&&!e.call(f=>Le(f,t),"callee",...n.callee.type==="ChainExpression"?["expression"]:[]))return Ni(e,t,r);let p=[s?"new ":"",Gi(e,r),i,rt(e,t,r),Lt(e,t,r)];return u||w(n.callee)?l(p):p}function Gi(e,t){let{node:r}=e;return r.type==="ImportExpression"?`import${r.phase?`.${r.phase}`:""}`:t("callee")}function el(e){let{node:t}=e;if(t.type!=="CallExpression"||t.optional||t.callee.type!=="Identifier")return!1;let r=le(t);return t.callee.name==="require"?r.length===1&&K(r[0])||r.length>1:t.callee.name==="define"&&e.parent.type==="ExpressionStatement"?r.length===1||r.length===2&&r[0].type==="ArrayExpression"||r.length===3&&K(r[0])&&r[1].type==="ArrayExpression":!1}function ht(e,t,r,n,s,u){let i=tl(e,t,r,n,u),a=u?r(u,{assignmentLayout:i}):"";switch(i){case"break-after-operator":return l([l(n),s,l(D([x,a]))]);case"never-break-after-operator":return l([l(n),s," ",a]);case"fluid":{let o=Symbol("assignment");return l([l(n),s,l(D(x),{id:o}),je,xt(a,{groupId:o})])}case"break-lhs":return l([n,s," ",l(a)]);case"chain":return[l(n),s,x,a];case"chain-tail":return[l(n),s,D([x,a])];case"chain-tail-arrow-chain":return[l(n),s,a];case"only-left":return n}}function Wi(e,t,r){let{node:n}=e;return ht(e,t,r,r("left"),[" ",n.operator],"right")}function Ui(e,t,r){return ht(e,t,r,r("id")," =","init")}function tl(e,t,r,n,s){let{node:u}=e,i=u[s];if(!i)return"only-left";let a=!zr(i);if(e.match(zr,Xi,y=>!a||y.type!=="ExpressionStatement"&&y.type!=="VariableDeclaration"))return a?i.type==="ArrowFunctionExpression"&&i.body.type==="ArrowFunctionExpression"?"chain-tail-arrow-chain":"chain-tail":"chain";if(!a&&zr(i.right)||de(t.originalText,i))return"break-after-operator";if(u.type==="ImportAttribute"||i.type==="CallExpression"&&i.callee.name==="require"||t.parser==="json5"||t.parser==="jsonc"||t.parser==="json")return"never-break-after-operator";let m=Lu(n);if(nl(u)||al(u)||hs(u)&&m)return"break-lhs";let f=pl(u,n,t);return e.call(()=>rl(e,t,r,f),s)?"break-after-operator":sl(u)?"break-lhs":!m&&(f||i.type==="TemplateLiteral"||i.type==="TaggedTemplateExpression"||Du(i)||ye(i)||i.type==="ClassExpression")?"never-break-after-operator":"fluid"}function rl(e,t,r,n){let s=e.node;if(Fe(s)&&!Ht(s))return!0;switch(s.type){case"StringLiteralTypeAnnotation":case"SequenceExpression":return!0;case"TSConditionalType":case"ConditionalTypeAnnotation":if(!t.experimentalTernaries&&!ml(s))break;return!0;case"ConditionalExpression":{if(!t.experimentalTernaries){let{test:p}=s;return Fe(p)&&!Ht(p)}let{consequent:a,alternate:o}=s;return a.type==="ConditionalExpression"||o.type==="ConditionalExpression"}case"ClassExpression":return O(s.decorators)}if(n)return!1;let u=s,i=[];for(;;)if(u.type==="UnaryExpression"||u.type==="AwaitExpression"||u.type==="YieldExpression"&&u.argument!==null)u=u.argument,i.push("argument");else if(u.type==="TSNonNullExpression")u=u.expression,i.push("expression");else break;return!!(K(u)||e.call(()=>Yi(e,t,r),...i))}function nl(e){if(Xi(e)){let t=e.left||e.id;return t.type==="ObjectPattern"&&t.properties.length>2&&t.properties.some(r=>{var n;return Te(r)&&(!r.shorthand||((n=r.value)==null?void 0:n.type)==="AssignmentPattern")})}return!1}function zr(e){return e.type==="AssignmentExpression"}function Xi(e){return zr(e)||e.type==="VariableDeclarator"}function sl(e){let t=il(e);if(O(t)){let r=e.type==="TSTypeAliasDeclaration"?"constraint":"bound";if(t.length>1&&t.some(n=>n[r]||n.default))return!0}return!1}var ul=R(["TSTypeAliasDeclaration","TypeAlias"]);function il(e){var t;if(ul(e))return(t=e.typeParameters)==null?void 0:t.params}function al(e){if(e.type!=="VariableDeclarator")return!1;let{typeAnnotation:t}=e.id;if(!t||!t.typeAnnotation)return!1;let r=qi(t.typeAnnotation);return O(r)&&r.length>1&&r.some(n=>O(qi(n))||n.type==="TSConditionalType")}function hs(e){var t;return e.type==="VariableDeclarator"&&((t=e.init)==null?void 0:t.type)==="ArrowFunctionExpression"}var ol=R(["TSTypeReference","GenericTypeAnnotation"]);function qi(e){var t;if(ol(e))return(t=e.typeArguments??e.typeParameters)==null?void 0:t.params}function Yi(e,t,r,n=!1){var i;let{node:s}=e,u=()=>Yi(e,t,r,!0);if(s.type==="ChainExpression"||s.type==="TSNonNullExpression")return e.call(u,"expression");if(w(s)){if((i=Qr(e,t,r).label)!=null&&i.memberChain)return!1;let o=le(s);return!(o.length===0||o.length===1&&ir(o[0],t))||cl(s,r)?!1:e.call(u,"callee")}return G(s)?e.call(u,"object"):n&&(s.type==="Identifier"||s.type==="ThisExpression")}function pl(e,t,r){return Te(e)?(t=Ut(t),typeof t=="string"&&st(t)<r.tabWidth+3):!1}function cl(e,t){let r=ll(e);if(O(r)){if(r.length>1)return!0;if(r.length===1){let s=r[0];if(Oe(s)||qt(s)||s.type==="TSTypeLiteral"||s.type==="ObjectTypeAnnotation")return!0}let n=e.typeParameters?"typeParameters":"typeArguments";if(re(t(n)))return!0}return!1}function ll(e){var t;return(t=e.typeParameters??e.typeArguments)==null?void 0:t.params}function ml(e){function t(r){switch(r.type){case"FunctionTypeAnnotation":case"GenericTypeAnnotation":case"TSFunctionType":return!!r.typeParameters;case"TSTypeReference":return!!(r.typeArguments??r.typeParameters);default:return!1}}return t(e.checkType)||t(e.extendsType)}function Ue(e,t,r,n,s){let u=e.node,i=Q(u),a=s?rt(e,t,r):"";if(i.length===0)return[a,"(",N(e,t,{filter:c=>ke(t.originalText,P(c))===")"}),")"];let{parent:o}=e,p=Pt(o),m=gs(u),f=[];if(hu(e,(c,A)=>{let T=A===i.length-1;T&&u.rest&&f.push("..."),f.push(r()),!T&&(f.push(","),p||m?f.push(" "):me(i[A],t)?f.push(F,F):f.push(x))}),n&&!fl(e)){if(re(a)||re(f))throw new Dt;return l([cr(a),"(",cr(f),")"])}let y=i.every(c=>!O(c.decorators));return m&&y?[a,"(",...f,")"]:p?[a,"(",...f,")"]:(Or(o)||Eu(o)||o.type==="TypeAlias"||o.type==="UnionTypeAnnotation"||o.type==="IntersectionTypeAnnotation"||o.type==="FunctionTypeAnnotation"&&o.returnType===u)&&i.length===1&&i[0].name===null&&u.this!==i[0]&&i[0].typeAnnotation&&u.typeParameters===null&&Nt(i[0].typeAnnotation)&&!u.rest?t.arrowParens==="always"||u.type==="HookTypeAnnotation"?["(",...f,")"]:f:[a,"(",D([E,...f]),S(!xu(u)&&ce(t,"all")?",":""),E,")"]}function gs(e){if(!e)return!1;let t=Q(e);if(t.length!==1)return!1;let[r]=t;return!d(r)&&(r.type==="ObjectPattern"||r.type==="ArrayPattern"||r.type==="Identifier"&&r.typeAnnotation&&(r.typeAnnotation.type==="TypeAnnotation"||r.typeAnnotation.type==="TSTypeAnnotation")&&Re(r.typeAnnotation.typeAnnotation)||r.type==="FunctionTypeParam"&&Re(r.typeAnnotation)&&r!==e.rest||r.type==="AssignmentPattern"&&(r.left.type==="ObjectPattern"||r.left.type==="ArrayPattern")&&(r.right.type==="Identifier"||ue(r.right)&&r.right.properties.length===0||U(r.right)&&r.right.elements.length===0))}function yl(e){let t;return e.returnType?(t=e.returnType,t.typeAnnotation&&(t=t.typeAnnotation)):e.typeAnnotation&&(t=e.typeAnnotation),t}function ct(e,t){var s;let r=yl(e);if(!r)return!1;let n=(s=e.typeParameters)==null?void 0:s.params;if(n){if(n.length>1)return!1;if(n.length===1){let u=n[0];if(u.constraint||u.default)return!1}}return Q(e).length===1&&(Re(r)||re(t))}function fl(e){return e.match(t=>t.type==="ArrowFunctionExpression"&&t.body.type==="BlockStatement",(t,r)=>{if(t.type==="CallExpression"&&r==="arguments"&&t.arguments.length===1&&t.callee.type==="CallExpression"){let n=t.callee.callee;return n.type==="Identifier"||n.type==="MemberExpression"&&!n.computed&&n.object.type==="Identifier"&&n.property.type==="Identifier"}return!1},(t,r)=>t.type==="VariableDeclarator"&&r==="init"||t.type==="ExportDefaultDeclaration"&&r==="declaration"||t.type==="TSExportAssignment"&&r==="expression"||t.type==="AssignmentExpression"&&r==="right"&&t.left.type==="MemberExpression"&&t.left.object.type==="Identifier"&&t.left.object.name==="module"&&t.left.property.type==="Identifier"&&t.left.property.name==="exports",t=>t.type!=="VariableDeclaration"||t.kind==="const"&&t.declarations.length===1)}function Hi(e){let t=Q(e);return t.length>1&&t.some(r=>r.type==="TSParameterProperty")}var Dl=R(["VoidTypeAnnotation","TSVoidKeyword","NullLiteralTypeAnnotation","TSNullKeyword"]),El=R(["ObjectTypeAnnotation","TSTypeLiteral","GenericTypeAnnotation","TSTypeReference"]);function Fl(e){let{types:t}=e;if(t.some(n=>d(n)))return!1;let r=t.find(n=>El(n));return r?t.every(n=>n===r||Dl(n)):!1}function Ss(e){return Nt(e)||Re(e)?!0:Oe(e)?Fl(e):!1}function Vi(e,t,r){let n=t.semi?";":"",{node:s}=e,u=[$(e),"opaque type ",r("id"),r("typeParameters")];return s.supertype&&u.push(": ",r("supertype")),s.impltype&&u.push(" = ",r("impltype")),u.push(n),u}function Zr(e,t,r){let n=t.semi?";":"",{node:s}=e,u=[$(e)];u.push("type ",r("id"),r("typeParameters"));let i=s.type==="TSTypeAliasDeclaration"?"typeAnnotation":"right";return[ht(e,t,r,u," =",i),n]}function en(e,t,r){let n=!1;return l(e.map(({isFirst:s,previous:u,node:i,index:a})=>{let o=r();if(s)return o;let p=Re(i),m=Re(u);return m&&p?[" & ",n?D(o):o]:!m&&!p||de(t.originalText,i)?t.experimentalOperatorPosition==="start"?D([x,"& ",o]):D([" &",x,o]):(a>1&&(n=!0),[" & ",a>1?D(o):o])},"types"))}function tn(e,t,r){let{node:n}=e,{parent:s}=e,u=s.type!=="TypeParameterInstantiation"&&(!Je(s)||!t.experimentalTernaries)&&s.type!=="TSTypeParameterInstantiation"&&s.type!=="GenericTypeAnnotation"&&s.type!=="TSTypeReference"&&s.type!=="TSTypeAssertion"&&s.type!=="TupleTypeAnnotation"&&s.type!=="TSTupleType"&&!(s.type==="FunctionTypeParam"&&!s.name&&e.grandparent.this!==s)&&!((s.type==="TypeAlias"||s.type==="VariableDeclarator"||s.type==="TSTypeAliasDeclaration")&&de(t.originalText,n)),i=Ss(n),a=e.map(m=>{let f=r();return i||(f=be(2,f)),Ee(m,f,t)},"types");if(i)return b(" | ",a);let o=u&&!de(t.originalText,n),p=[S([o?x:"","| "]),b([x,"| "],a)];return Le(e,t)?l([D(p),E]):(s.type==="TupleTypeAnnotation"||s.type==="TSTupleType")&&s[s.type==="TupleTypeAnnotation"&&s.types?"types":"elementTypes"].length>1?l([D([S(["(",E]),p]),E,S(")")]):l(u?D(p):p)}function Cl(e){var n;let{node:t,parent:r}=e;return t.type==="FunctionTypeAnnotation"&&(Or(r)||!((r.type==="ObjectTypeProperty"||r.type==="ObjectTypeInternalSlot")&&!r.variance&&!r.optional&&Bt(r,t)||r.type==="ObjectTypeCallProperty"||((n=e.getParentNode(2))==null?void 0:n.type)==="DeclareFunction"))}function rn(e,t,r){let{node:n}=e,s=[Vt(e)];(n.type==="TSConstructorType"||n.type==="TSConstructSignatureDeclaration")&&s.push("new ");let u=Ue(e,t,r,!1,!0),i=[];return n.type==="FunctionTypeAnnotation"?i.push(Cl(e)?" => ":": ",r("returnType")):i.push(H(e,r,n.returnType?"returnType":"typeAnnotation")),ct(n,i)&&(u=l(u)),s.push(u,i),l(s)}function nn(e,t,r){return[r("objectType"),V(e),"[",r("indexType"),"]"]}function sn(e,t,r){return["infer ",r("typeParameter")]}function Bs(e,t,r){let{node:n}=e;return[n.postfix?"":r,H(e,t),n.postfix?r:""]}function un(e,t,r){let{node:n}=e;return["...",...n.type==="TupleTypeSpreadElement"&&n.label?[r("label"),": "]:[],r("typeAnnotation")]}function an(e,t,r){let{node:n}=e;return[n.variance?r("variance"):"",r("label"),n.optional?"?":"",": ",r("elementType")]}var Al=new WeakSet;function H(e,t,r="typeAnnotation"){let{node:{[r]:n}}=e;if(!n)return"";let s=!1;if(n.type==="TSTypeAnnotation"||n.type==="TypeAnnotation"){let u=e.call($i,r);(u==="=>"||u===":"&&d(n,h.Leading))&&(s=!0),Al.add(n)}return s?[" ",t(r)]:t(r)}var $i=e=>e.match(t=>t.type==="TSTypeAnnotation",(t,r)=>(r==="returnType"||r==="typeAnnotation")&&(t.type==="TSFunctionType"||t.type==="TSConstructorType"))?"=>":e.match(t=>t.type==="TSTypeAnnotation",(t,r)=>r==="typeAnnotation"&&(t.type==="TSJSDocNullableType"||t.type==="TSJSDocNonNullableType"||t.type==="TSTypePredicate"))||e.match(t=>t.type==="TypeAnnotation",(t,r)=>r==="typeAnnotation"&&t.type==="Identifier",(t,r)=>r==="id"&&t.type==="DeclareFunction")||e.match(t=>t.type==="TypeAnnotation",(t,r)=>r==="typeAnnotation"&&t.type==="Identifier",(t,r)=>r==="id"&&t.type==="DeclareHook")||e.match(t=>t.type==="TypeAnnotation",(t,r)=>r==="bound"&&t.type==="TypeParameter"&&t.usesExtendsBound)?"":":";function on(e,t,r){let n=$i(e);return n?[n," ",r("typeAnnotation")]:r("typeAnnotation")}function pn(e){return[e("elementType"),"[]"]}function cn({node:e},t){let r=e.type==="TSTypeQuery"?"exprName":"argument",n=e.type==="TypeofTypeAnnotation"||e.typeArguments?"typeArguments":"typeParameters";return["typeof ",t(r),t(n)]}function ln(e,t){let{node:r}=e;return[r.type==="TSTypePredicate"&&r.asserts?"asserts ":r.type==="TypePredicate"&&r.kind?`${r.kind} `:"",t("parameterName"),r.typeAnnotation?[" is ",H(e,t)]:""]}function V(e){let{node:t}=e;return!t.optional||t.type==="Identifier"&&t===e.parent.key?"":w(t)||G(t)&&t.computed||t.type==="OptionalIndexedAccessType"?"?.":"?"}function mn(e){return e.node.definite||e.match(void 0,(t,r)=>r==="id"&&t.type==="VariableDeclarator"&&t.definite)?"!":""}var dl=new Set(["DeclareClass","DeclareComponent","DeclareFunction","DeclareHook","DeclareVariable","DeclareExportDeclaration","DeclareExportAllDeclaration","DeclareOpaqueType","DeclareTypeAlias","DeclareEnum","DeclareInterface"]);function $(e){let{node:t}=e;return t.declare||dl.has(t.type)&&e.parent.type!=="DeclareExportDeclaration"?"declare ":""}var Tl=new Set(["TSAbstractMethodDefinition","TSAbstractPropertyDefinition","TSAbstractAccessorProperty"]);function Vt({node:e}){return e.abstract||Tl.has(e.type)?"abstract ":""}function rt(e,t,r){let n=e.node;return n.typeArguments?r("typeArguments"):n.typeParameters?r("typeParameters"):""}function Kr(e,t,r){return["::",r("callee")]}function Et(e,t,r){return e.type==="EmptyStatement"?";":e.type==="BlockStatement"||r?[" ",t]:D([x,t])}function yn(e,t){return["...",t("argument"),H(e,t)]}function $t(e){return e.accessibility?e.accessibility+" ":""}function xl(e,t,r,n){let{node:s}=e,u=s.inexact?"...":"";return d(s,h.Dangling)?l([r,u,N(e,t,{indent:!0}),E,n]):[r,u,n]}function Kt(e,t,r){let{node:n}=e,s=[],u="[",i="]",a=n.type==="TupleTypeAnnotation"&&n.types?"types":n.type==="TSTupleType"||n.type==="TupleTypeAnnotation"?"elementTypes":"elements",o=n[a];if(o.length===0)s.push(xl(e,t,u,i));else{let p=v(!1,o,-1),m=(p==null?void 0:p.type)!=="RestElement"&&!n.inexact,f=p===null,y=Symbol("array"),C=!t.__inJestEach&&o.length>1&&o.every((T,B,g)=>{let _=T==null?void 0:T.type;if(!U(T)&&!ue(T))return!1;let M=g[B+1];if(M&&_!==M.type)return!1;let J=U(T)?"elements":"properties";return T[J]&&T[J].length>1}),c=Ts(n,t),A=m?f?",":ce(t)?c?S(",","",{groupId:y}):S(","):"":"";s.push(l([u,D([E,c?gl(e,t,r,A):[hl(e,t,r,a,n.inexact),A],N(e,t)]),E,i],{shouldBreak:C,id:y}))}return s.push(V(e),H(e,r)),s}function Ts(e,t){return U(e)&&e.elements.length>1&&e.elements.every(r=>r&&(ye(r)||Rn(r)&&!d(r.argument))&&!d(r,h.Trailing|h.Line,n=>!ee(t.originalText,j(n),{backwards:!0})))}function Ki({node:e},{originalText:t}){let r=s=>_t(t,Mt(t,s)),n=s=>t[s]===","?s:n(r(s+1));return vt(t,n(P(e)))}function hl(e,t,r,n,s){let u=[];return e.each(({node:i,isLast:a})=>{u.push(i?l(r()):""),(!a||s)&&u.push([",",x,i&&Ki(e,t)?E:""])},n),s&&u.push("..."),u}function gl(e,t,r,n){let s=[];return e.each(({isLast:u,next:i})=>{s.push([r(),u?n:","]),u||s.push(Ki(e,t)?[F,F]:d(i,h.Leading|h.Line)?F:x)},"elements"),Nr(s)}var Sl=/^[\$A-Z_a-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC][\$0-9A-Z_a-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]*$/,Bl=e=>Sl.test(e),Qi=Bl;function bl(e){return e.length===1?e:e.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(?=\d)/u,"$1$2").replace(/^([+-]?[\d.]+)e[+-]?0+$/u,"$1").replace(/^([+-])?\./u,"$10.").replace(/(\.\d+?)0+(?=e|$)/u,"$1").replace(/\.(?=e|$)/u,"")}var Ft=bl;var fn=new WeakMap;function Zi(e){return/^(?:\d+|\d+\.\d+)$/u.test(e)}function zi(e,t){return t.parser==="json"||t.parser==="jsonc"||!K(e.key)||ut(ae(e.key),t).slice(1,-1)!==e.key.value?!1:!!(Qi(e.key.value)&&!(t.parser==="babel-ts"&&e.type==="ClassProperty"||(t.parser==="typescript"||t.parser==="oxc-ts")&&e.type==="PropertyDefinition")||Zi(e.key.value)&&String(Number(e.key.value))===e.key.value&&e.type!=="ImportAttribute"&&(t.parser==="babel"||t.parser==="acorn"||t.parser==="oxc"||t.parser==="espree"||t.parser==="meriyah"||t.parser==="__babel_estree"))}function Pl(e,t){let{key:r}=e.node;return(r.type==="Identifier"||ye(r)&&Zi(Ft(ae(r)))&&String(r.value)===Ft(ae(r))&&!(t.parser==="typescript"||t.parser==="babel-ts"||t.parser==="oxc-ts"))&&(t.parser==="json"||t.parser==="jsonc"||t.quoteProps==="consistent"&&fn.get(e.parent))}function Ct(e,t,r){let{node:n}=e;if(n.computed)return["[",r("key"),"]"];let{parent:s}=e,{key:u}=n;if(t.quoteProps==="consistent"&&!fn.has(s)){let i=e.siblings.some(a=>!a.computed&&K(a.key)&&!zi(a,t));fn.set(s,i)}if(Pl(e,t)){let i=ut(JSON.stringify(u.type==="Identifier"?u.name:u.value.toString()),t);return e.call(a=>Ee(a,i,t),"key")}return zi(n,t)&&(t.quoteProps==="as-needed"||t.quoteProps==="consistent"&&!fn.get(s))?e.call(i=>Ee(i,/^\d/u.test(u.value)?Ft(u.value):u.value,t),"key"):r("key")}function Dn(e,t,r){let{node:n}=e;return n.shorthand?r("value"):ht(e,t,r,Ct(e,t,r),":","value")}var kl=({node:e,key:t,parent:r})=>t==="value"&&e.type==="FunctionExpression"&&(r.type==="ObjectMethod"||r.type==="ClassMethod"||r.type==="ClassPrivateMethod"||r.type==="MethodDefinition"||r.type==="TSAbstractMethodDefinition"||r.type==="TSDeclareMethod"||r.type==="Property"&&bt(r));function En(e,t,r,n){if(kl(e))return Fn(e,t,r);let{node:s}=e,u=!1;if((s.type==="FunctionDeclaration"||s.type==="FunctionExpression")&&(n!=null&&n.expandLastArg)){let{parent:m}=e;w(m)&&(le(m).length>1||Q(s).every(f=>f.type==="Identifier"&&!f.typeAnnotation))&&(u=!0)}let i=[$(e),s.async?"async ":"",`function${s.generator?"*":""} `,s.id?r("id"):""],a=Ue(e,t,r,u),o=Qt(e,r),p=ct(s,o);return i.push(rt(e,t,r),l([p?l(a):a,o]),s.body?" ":"",r("body")),t.semi&&(s.declare||!s.body)&&i.push(";"),i}function Er(e,t,r){let{node:n}=e,{kind:s}=n,u=n.value||n,i=[];return!s||s==="init"||s==="method"||s==="constructor"?u.async&&i.push("async "):(jt.ok(s==="get"||s==="set"),i.push(s," ")),u.generator&&i.push("*"),i.push(Ct(e,t,r),n.optional?"?":"",n===u?Fn(e,t,r):r("value")),i}function Fn(e,t,r){let{node:n}=e,s=Ue(e,t,r),u=Qt(e,r),i=Hi(n),a=ct(n,u),o=[rt(e,t,r),l([i?l(s,{shouldBreak:!0}):a?l(s):s,u])];return n.body?o.push(" ",r("body")):o.push(t.semi?";":""),o}function Il(e){let t=Q(e);return t.length===1&&!e.typeParameters&&!d(e,h.Dangling)&&t[0].type==="Identifier"&&!t[0].typeAnnotation&&!d(t[0])&&!t[0].optional&&!e.predicate&&!e.returnType}function Cn(e,t){if(t.arrowParens==="always")return!1;if(t.arrowParens==="avoid"){let{node:r}=e;return Il(r)}return!1}function Qt(e,t){let{node:r}=e,s=[H(e,t,"returnType")];return r.predicate&&s.push(t("predicate")),s}function ea(e,t,r){let{node:n}=e,s=t.semi?";":"",u=[];if(n.argument){let o=r("argument");Ll(t,n.argument)?o=["(",D([F,o]),F,")"]:(Fe(n.argument)||t.experimentalTernaries&&n.argument.type==="ConditionalExpression"&&(n.argument.consequent.type==="ConditionalExpression"||n.argument.alternate.type==="ConditionalExpression"))&&(o=l([S("("),D([E,o]),E,S(")")])),u.push(" ",o)}let i=d(n,h.Dangling),a=s&&i&&d(n,h.Last|h.Line);return a&&u.push(s),i&&u.push(" ",N(e,t)),a||u.push(s),u}function ta(e,t,r){return["return",ea(e,t,r)]}function ra(e,t,r){return["throw",ea(e,t,r)]}function Ll(e,t){if(de(e.originalText,t)||d(t,h.Leading,r=>ie(e.originalText,j(r),P(r)))&&!Y(t))return!0;if(Jt(t)){let r=t,n;for(;n=mu(r);)if(r=n,de(e.originalText,r))return!0}return!1}var bs=new WeakMap;function na(e){return bs.has(e)||bs.set(e,e.type==="ConditionalExpression"&&!pe(e,t=>t.type==="ObjectExpression")),bs.get(e)}var wl=e=>e.type==="SequenceExpression";function sa(e,t,r,n={}){let s=[],u,i=[],a=!1,o=!n.expandLastArg&&e.node.body.type==="ArrowFunctionExpression",p;(function B(){let{node:g}=e,_=Ol(e,t,r,n);if(s.length===0)s.push(_);else{let{leading:M,trailing:J}=fs(e,t);s.push([M,_]),i.unshift(J)}o&&(a||(a=g.returnType&&Q(g).length>0||g.typeParameters||Q(g).some(M=>M.type!=="Identifier"))),!o||g.body.type!=="ArrowFunctionExpression"?(u=r("body",n),p=g.body):e.call(B,"body")})();let m=!de(t.originalText,p)&&(wl(p)||_l(p,u,t)||!a&&na(p)),f=e.key==="callee"&&yt(e.parent),y=Symbol("arrow-chain"),C=Ml(e,n,{signatureDocs:s,shouldBreak:a}),c=!1,A=!1,T=!1;return o&&(f||n.assignmentLayout)&&(A=!0,T=!d(e.node,h.Leading&h.Line),c=n.assignmentLayout==="chain-tail-arrow-chain"||f&&!m),u=vl(e,t,n,{bodyDoc:u,bodyComments:i,functionBody:p,shouldPutBodyOnSameLine:m}),l([l(A?D([T?E:"",C]):C,{shouldBreak:c,id:y})," =>",o?xt(u,{groupId:y}):l(u),o&&f?S(E,"",{groupId:y}):""])}function Ol(e,t,r,n){let{node:s}=e,u=[];if(s.async&&u.push("async "),Cn(e,t))u.push(r(["params",0]));else{let a=n.expandLastArg||n.expandFirstArg,o=Qt(e,r);if(a){if(re(o))throw new Dt;o=l(cr(o))}u.push(l([Ue(e,t,r,a,!0),o]))}let i=N(e,t,{filter(a){let o=ot(t.originalText,P(a));return o!==!1&&t.originalText.slice(o,o+2)==="=>"}});return i&&u.push(" ",i),u}function _l(e,t,r){var n,s;return U(e)||ue(e)||e.type==="ArrowFunctionExpression"||e.type==="DoExpression"||e.type==="BlockStatement"||Y(e)||((n=t.label)==null?void 0:n.hug)!==!1&&(((s=t.label)==null?void 0:s.embed)||_r(e,r.originalText))}function Ml(e,t,{signatureDocs:r,shouldBreak:n}){if(r.length===1)return r[0];let{parent:s,key:u}=e;return u!=="callee"&&yt(s)||Fe(s)?l([r[0]," =>",D([x,b([" =>",x],r.slice(1))])],{shouldBreak:n}):u==="callee"&&yt(s)||t.assignmentLayout?l(b([" =>",x],r),{shouldBreak:n}):l(D(b([" =>",x],r)),{shouldBreak:n})}function vl(e,t,r,{bodyDoc:n,bodyComments:s,functionBody:u,shouldPutBodyOnSameLine:i}){let{node:a,parent:o}=e,p=r.expandLastArg&&ce(t,"all")?S(","):"",m=(r.expandLastArg||o.type==="JSXExpressionContainer")&&!d(a)?E:"";return i&&na(u)?[" ",l([S("","("),D([E,n]),S("",")"),p,m]),s]:i?[" ",n,s]:[D([x,n,s]),p,m]}var jl=(e,t,r)=>{if(!(e&&t==null)){if(t.findLast)return t.findLast(r);for(let n=t.length-1;n>=0;n--){let s=t[n];if(r(s,n,t))return s}}},ua=jl;function Fr(e,t,r,n){let{node:s}=e,u=[],i=ua(!1,s[n],a=>a.type!=="EmptyStatement");return e.each(({node:a})=>{a.type!=="EmptyStatement"&&(u.push(r()),a!==i&&(u.push(F),me(a,t)&&u.push(F)))},n),u}function An(e,t,r){let n=Rl(e,t,r),{node:s,parent:u}=e;if(s.type==="Program"&&(u==null?void 0:u.type)!=="ModuleExpression")return n?[n,F]:"";let i=[];if(s.type==="StaticBlock"&&i.push("static "),i.push("{"),n)i.push(D([F,n]),F);else{let a=e.grandparent;u.type==="ArrowFunctionExpression"||u.type==="FunctionExpression"||u.type==="FunctionDeclaration"||u.type==="ComponentDeclaration"||u.type==="HookDeclaration"||u.type==="ObjectMethod"||u.type==="ClassMethod"||u.type==="ClassPrivateMethod"||u.type==="ForStatement"||u.type==="WhileStatement"||u.type==="DoWhileStatement"||u.type==="DoExpression"||u.type==="ModuleExpression"||u.type==="CatchClause"&&!a.finalizer||u.type==="TSModuleDeclaration"||s.type==="StaticBlock"||i.push(F)}return i.push("}"),i}function Rl(e,t,r){let{node:n}=e,s=O(n.directives),u=n.body.some(o=>o.type!=="EmptyStatement"),i=d(n,h.Dangling);if(!s&&!u&&!i)return"";let a=[];return s&&(a.push(Fr(e,t,r,"directives")),(u||i)&&(a.push(F),me(v(!1,n.directives,-1),t)&&a.push(F))),u&&a.push(Fr(e,t,r,"body")),i&&a.push(N(e,t)),a}function Jl(e){let t=new WeakMap;return function(r){return t.has(r)||t.set(r,Symbol(e)),t.get(r)}}var dn=Jl;var Cr=dn("typeParameters");function Nl(e,t,r){let{node:n}=e;return Q(n).length===1&&n.type.startsWith("TS")&&!n[r][0].constraint&&e.parent.type==="ArrowFunctionExpression"&&!(t.filepath&&/\.ts$/u.test(t.filepath))}function wt(e,t,r,n){let{node:s}=e;if(!s[n])return"";if(!Array.isArray(s[n]))return r(n);let u=Pt(e.grandparent),i=e.match(p=>!(p[n].length===1&&Re(p[n][0])),void 0,(p,m)=>m==="typeAnnotation",p=>p.type==="Identifier",hs);if(s[n].length===0||!i&&(u||s[n].length===1&&(s[n][0].type==="NullableTypeAnnotation"||Ss(s[n][0]))))return["<",b(", ",e.map(r,n)),Gl(e,t),">"];let o=s.type==="TSTypeParameterInstantiation"?"":Nl(e,t,n)?",":ce(t)?S(","):"";return l(["<",D([E,b([",",x],e.map(r,n))]),o,E,">"],{id:Cr(s)})}function Gl(e,t){let{node:r}=e;if(!d(r,h.Dangling))return"";let n=!d(r,h.Line),s=N(e,t,{indent:!n});return n?s:[s,F]}function Tn(e,t,r){let{node:n}=e,s=[n.const?"const ":""],u=n.type==="TSTypeParameter"?r("name"):n.name;if(n.variance&&s.push(r("variance")),n.in&&s.push("in "),n.out&&s.push("out "),s.push(u),n.bound&&(n.usesExtendsBound&&s.push(" extends "),s.push(H(e,r,"bound"))),n.constraint){let i=Symbol("constraint");s.push(" extends",l(D(x),{id:i}),je,xt(r("constraint"),{groupId:i}))}return n.default&&s.push(" = ",r("default")),l(s)}var ia=R(["ClassProperty","PropertyDefinition","ClassPrivateProperty","ClassAccessorProperty","AccessorProperty","TSAbstractPropertyDefinition","TSAbstractAccessorProperty"]);function xn(e,t,r){let{node:n}=e,s=[$(e),Vt(e),"class"],u=d(n.id,h.Trailing)||d(n.typeParameters,h.Trailing)||d(n.superClass)||O(n.extends)||O(n.mixins)||O(n.implements),i=[],a=[];if(n.id&&i.push(" ",r("id")),i.push(r("typeParameters")),n.superClass){let m=[Wl(e,t,r),r(n.superTypeArguments?"superTypeArguments":"superTypeParameters")],f=e.call(y=>["extends ",Ee(y,m,t)],"superClass");u?a.push(x,l(f)):a.push(" ",f)}else a.push(Ps(e,t,r,"extends"));a.push(Ps(e,t,r,"mixins"),Ps(e,t,r,"implements"));let o;if(u){let m;pa(n)?m=[...i,D(a)]:m=D([...i,a]),o=aa(n),s.push(l(m,{id:o}))}else s.push(...i,...a);let p=n.body;return u&&O(p.body)?s.push(S(F," ",{groupId:o})):s.push(" "),s.push(r("body")),s}var aa=dn("heritageGroup");function oa(e){return S(F,"",{groupId:aa(e)})}function ql(e){return["extends","mixins","implements"].reduce((t,r)=>t+(Array.isArray(e[r])?e[r].length:0),e.superClass?1:0)>1}function pa(e){return e.typeParameters&&!d(e.typeParameters,h.Trailing|h.Line)&&!ql(e)}function Ps(e,t,r,n){let{node:s}=e;if(!O(s[n]))return"";let u=N(e,t,{marker:n});return[pa(s)?S(" ",x,{groupId:Cr(s.typeParameters)}):x,u,u&&F,n,l(D([x,b([",",x],e.map(r,n))]))]}function Wl(e,t,r){let n=r("superClass"),{parent:s}=e;return s.type==="AssignmentExpression"?l(S(["(",D([E,n]),E,")"],n)):n}function hn(e,t,r){let{node:n}=e,s=[];return O(n.decorators)&&s.push(ds(e,t,r)),s.push($t(n)),n.static&&s.push("static "),s.push(Vt(e)),n.override&&s.push("override "),s.push(Er(e,t,r)),s}function gn(e,t,r){let{node:n}=e,s=[],u=t.semi?";":"";O(n.decorators)&&s.push(ds(e,t,r)),s.push($(e),$t(n)),n.static&&s.push("static "),s.push(Vt(e)),n.override&&s.push("override "),n.readonly&&s.push("readonly "),n.variance&&s.push(r("variance")),(n.type==="ClassAccessorProperty"||n.type==="AccessorProperty"||n.type==="TSAbstractAccessorProperty")&&s.push("accessor "),s.push(Ct(e,t,r),V(e),mn(e),H(e,r));let i=n.type==="TSAbstractPropertyDefinition"||n.type==="TSAbstractAccessorProperty";return[ht(e,t,r,s," =",i?void 0:"value"),u]}function ca(e,t,r){let{node:n}=e,s=[];return e.each(({node:u,next:i,isLast:a})=>{s.push(r()),!t.semi&&ia(u)&&Ul(u,i)&&s.push(";"),a||(s.push(F),me(u,t)&&s.push(F))},"body"),d(n,h.Dangling)&&s.push(N(e,t)),["{",s.length>0?[D([F,s]),F]:"","}"]}function Ul(e,t){var s;let{type:r,name:n}=e.key;if(!e.computed&&r==="Identifier"&&(n==="static"||n==="get"||n==="set")&&!e.value&&!e.typeAnnotation)return!0;if(!t||t.static||t.accessibility||t.readonly)return!1;if(!t.computed){let u=(s=t.key)==null?void 0:s.name;if(u==="in"||u==="instanceof")return!0}if(ia(t)&&t.variance&&!t.static&&!t.declare)return!0;switch(t.type){case"ClassProperty":case"PropertyDefinition":case"TSAbstractPropertyDefinition":return t.computed;case"MethodDefinition":case"TSAbstractMethodDefinition":case"ClassMethod":case"ClassPrivateMethod":{if((t.value?t.value.async:t.async)||t.kind==="get"||t.kind==="set")return!1;let i=t.value?t.value.generator:t.generator;return!!(t.computed||i)}case"TSIndexSignature":return!0}return!1}var Xl=R(["TSAsExpression","TSTypeAssertion","TSNonNullExpression","TSInstantiationExpression","TSSatisfiesExpression"]);function ks(e){return Xl(e)?ks(e.expression):e}var la=R(["FunctionExpression","ArrowFunctionExpression"]);function ma(e){return e.type==="MemberExpression"||e.type==="OptionalMemberExpression"||e.type==="Identifier"&&e.name!=="undefined"}function ya(e,t){if(t.semi||Is(e,t)||Ls(e,t))return!1;let{node:r,key:n,parent:s}=e;return!!(r.type==="ExpressionStatement"&&(n==="body"&&(s.type==="Program"||s.type==="BlockStatement"||s.type==="StaticBlock"||s.type==="TSModuleBlock")||n==="consequent"&&s.type==="SwitchCase")&&e.call(()=>fa(e,t),"expression"))}function fa(e,t){let{node:r}=e;switch(r.type){case"ParenthesizedExpression":case"TypeCastExpression":case"ArrayExpression":case"ArrayPattern":case"TemplateLiteral":case"TemplateElement":case"RegExpLiteral":return!0;case"ArrowFunctionExpression":if(!Cn(e,t))return!0;break;case"UnaryExpression":{let{prefix:n,operator:s}=r;if(n&&(s==="+"||s==="-"))return!0;break}case"BindExpression":if(!r.object)return!0;break;case"Literal":if(r.regex)return!0;break;default:if(Y(r))return!0}return Le(e,t)?!0:Jt(r)?e.call(()=>fa(e,t),...Lr(r)):!1}function Is({node:e,parent:t},r){return(r.parentParser==="markdown"||r.parentParser==="mdx")&&e.type==="ExpressionStatement"&&Y(e.expression)&&t.type==="Program"&&t.body.length===1}function Ls({node:e,parent:t},r){return(r.parser==="__vue_event_binding"||r.parser==="__vue_ts_event_binding")&&e.type==="ExpressionStatement"&&t.type==="Program"&&t.body.length===1}function Da(e,t,r){let n=[r("expression")];if(Ls(e,t)){let s=ks(e.node.expression);(la(s)||ma(s))&&n.push(";")}else Is(e,t)||t.semi&&n.push(";");return n}function Ea(e,t,r){if(t.__isVueBindings||t.__isVueForBindingLeft){let n=e.map(r,"program","body",0,"params");if(n.length===1)return n[0];let s=b([",",x],n);return t.__isVueForBindingLeft?["(",D([E,l(s)]),E,")"]:s}if(t.__isEmbeddedTypescriptGenericParameters){let n=e.map(r,"program","body",0,"typeParameters","params");return b([",",x],n)}}function Aa(e,t){let{node:r}=e;switch(r.type){case"RegExpLiteral":return Fa(r);case"BigIntLiteral":return Sn(r.extra.raw);case"NumericLiteral":return Ft(r.extra.raw);case"StringLiteral":return ve(ut(r.extra.raw,t));case"NullLiteral":return"null";case"BooleanLiteral":return String(r.value);case"DirectiveLiteral":return Ca(r.extra.raw,t);case"Literal":{if(r.regex)return Fa(r.regex);if(r.bigint)return Sn(r.raw);let{value:n}=r;return typeof n=="number"?Ft(r.raw):typeof n=="string"?Yl(e)?Ca(r.raw,t):ve(ut(r.raw,t)):String(n)}}}function Yl(e){if(e.key!=="expression")return;let{parent:t}=e;return t.type==="ExpressionStatement"&&typeof t.directive=="string"}function Sn(e){return e.toLowerCase()}function Fa({pattern:e,flags:t}){return t=[...t].sort().join(""),`/${e}/${t}`}function Ca(e,t){let r=e.slice(1,-1);if(r.includes('"')||r.includes("'"))return e;let n=t.singleQuote?"'":'"';return n+r+n}function Hl(e,t,r){let n=e.originalText.slice(t,r);for(let s of e[Symbol.for("comments")]){let u=j(s);if(u>r)break;let i=P(s);if(i<t)continue;let a=i-u;n=n.slice(0,u-t)+" ".repeat(a)+n.slice(i-t)}return n}var Ar=Hl;function da(e,t,r){let{node:n}=e;return["import",n.phase?` ${n.phase}`:"",Os(n),ha(e,t,r),xa(e,t,r),Sa(e,t,r),t.semi?";":""]}var Ta=e=>e.type==="ExportDefaultDeclaration"||e.type==="DeclareExportDeclaration"&&e.default;function Bn(e,t,r){let{node:n}=e,s=[wi(e,t,r),$(e),"export",Ta(n)?" default":""],{declaration:u,exported:i}=n;return d(n,h.Dangling)&&(s.push(" ",N(e,t)),Mr(n)&&s.push(F)),u?s.push(" ",r("declaration")):(s.push(Kl(n)),n.type==="ExportAllDeclaration"||n.type==="DeclareExportAllDeclaration"?(s.push(" *"),i&&s.push(" as ",r("exported"))):s.push(ha(e,t,r)),s.push(xa(e,t,r),Sa(e,t,r))),s.push($l(n,t)),s}var Vl=R(["ClassDeclaration","ComponentDeclaration","FunctionDeclaration","TSInterfaceDeclaration","DeclareClass","DeclareComponent","DeclareFunction","DeclareHook","HookDeclaration","TSDeclareFunction","EnumDeclaration"]);function $l(e,t){return t.semi&&(!e.declaration||Ta(e)&&!Vl(e.declaration))?";":""}function ws(e,t=!0){return e&&e!=="value"?`${t?" ":""}${e}${t?"":" "}`:""}function Os(e,t){return ws(e.importKind,t)}function Kl(e){return ws(e.exportKind)}function xa(e,t,r){let{node:n}=e;if(!n.source)return"";let s=[];return ga(n,t)&&s.push(" from"),s.push(" ",r("source")),s}function ha(e,t,r){let{node:n}=e;if(!ga(n,t))return"";let s=[" "];if(O(n.specifiers)){let u=[],i=[];e.each(()=>{let a=e.node.type;if(a==="ExportNamespaceSpecifier"||a==="ExportDefaultSpecifier"||a==="ImportNamespaceSpecifier"||a==="ImportDefaultSpecifier")u.push(r());else if(a==="ExportSpecifier"||a==="ImportSpecifier")i.push(r());else throw new qe(n,"specifier")},"specifiers"),s.push(b(", ",u)),i.length>0&&(u.length>0&&s.push(", "),i.length>1||u.length>0||n.specifiers.some(o=>d(o))?s.push(l(["{",D([t.bracketSpacing?x:E,b([",",x],i)]),S(ce(t)?",":""),t.bracketSpacing?x:E,"}"])):s.push(["{",t.bracketSpacing?" ":"",...i,t.bracketSpacing?" ":"","}"]))}else s.push("{}");return s}function ga(e,t){return e.type!=="ImportDeclaration"||O(e.specifiers)||e.importKind==="type"?!0:Ar(t,j(e),j(e.source)).trimEnd().endsWith("from")}function Ql(e,t){var n,s;if((n=e.extra)!=null&&n.deprecatedAssertSyntax)return"assert";let r=Ar(t,P(e.source),(s=e.attributes)!=null&&s[0]?j(e.attributes[0]):P(e)).trimStart();return r.startsWith("assert")?"assert":r.startsWith("with")||O(e.attributes)?"with":void 0}function Sa(e,t,r){let{node:n}=e;if(!n.source)return"";let s=Ql(n,t);if(!s)return"";let u=[` ${s} {`];return O(n.attributes)&&(t.bracketSpacing&&u.push(" "),u.push(b(", ",e.map(r,"attributes"))),t.bracketSpacing&&u.push(" ")),u.push("}"),u}function Ba(e,t,r){let{node:n}=e,{type:s}=n,u=s.startsWith("Import"),i=u?"imported":"local",a=u?"local":"exported",o=n[i],p=n[a],m="",f="";return s==="ExportNamespaceSpecifier"||s==="ImportNamespaceSpecifier"?m="*":o&&(m=r(i)),p&&!zl(n)&&(f=r(a)),[ws(s==="ImportSpecifier"?n.importKind:n.exportKind,!1),m,m&&f?" as ":"",f]}function zl(e){if(e.type!=="ImportSpecifier"&&e.type!=="ExportSpecifier")return!1;let{local:t,[e.type==="ImportSpecifier"?"imported":"exported"]:r}=e;if(t.type!==r.type||!au(t,r))return!1;if(K(t))return t.value===r.value&&ae(t)===ae(r);switch(t.type){case"Identifier":return t.name===r.name;default:return!1}}function gt(e,t,r){var J;let n=t.semi?";":"",{node:s}=e,u=s.type==="ObjectTypeAnnotation",i=s.type==="TSEnumBody"||s.type==="EnumBooleanBody"||s.type==="EnumNumberBody"||s.type==="EnumBigIntBody"||s.type==="EnumStringBody"||s.type==="EnumSymbolBody",a=[s.type==="TSTypeLiteral"||i?"members":s.type==="TSInterfaceBody"?"body":"properties"];u&&a.push("indexers","callProperties","internalSlots");let o=a.flatMap(I=>e.map(({node:q})=>({node:q,printed:r(),loc:j(q)}),I));a.length>1&&o.sort((I,q)=>I.loc-q.loc);let{parent:p,key:m}=e,f=u&&m==="body"&&(p.type==="InterfaceDeclaration"||p.type==="DeclareInterface"||p.type==="DeclareClass"),y=s.type==="TSInterfaceBody"||i||f||s.type==="ObjectPattern"&&p.type!=="FunctionDeclaration"&&p.type!=="FunctionExpression"&&p.type!=="ArrowFunctionExpression"&&p.type!=="ObjectMethod"&&p.type!=="ClassMethod"&&p.type!=="ClassPrivateMethod"&&p.type!=="AssignmentPattern"&&p.type!=="CatchClause"&&s.properties.some(I=>I.value&&(I.value.type==="ObjectPattern"||I.value.type==="ArrayPattern"))||s.type!=="ObjectPattern"&&t.objectWrap==="preserve"&&o.length>0&&ie(t.originalText,j(s),o[0].loc),C=f?";":s.type==="TSInterfaceBody"||s.type==="TSTypeLiteral"?S(n,";"):",",c=s.exact?"{|":"{",A=s.exact?"|}":"}",T=[],B=o.map(I=>{let q=[...T,l(I.printed)];return T=[C,x],(I.node.type==="TSPropertySignature"||I.node.type==="TSMethodSignature"||I.node.type==="TSConstructSignatureDeclaration"||I.node.type==="TSCallSignatureDeclaration")&&d(I.node,h.PrettierIgnore)&&T.shift(),me(I.node,t)&&T.push(F),q});if(s.inexact||s.hasUnknownMembers){let I;if(d(s,h.Dangling)){let q=d(s,h.Line);I=[N(e,t),q||ee(t.originalText,P(v(!1,Ve(s),-1)))?F:x,"..."]}else I=["..."];B.push([...T,...I])}let g=(J=v(!1,o,-1))==null?void 0:J.node,_=!(s.inexact||s.hasUnknownMembers||g&&(g.type==="RestElement"||(g.type==="TSPropertySignature"||g.type==="TSCallSignatureDeclaration"||g.type==="TSMethodSignature"||g.type==="TSConstructSignatureDeclaration"||g.type==="TSIndexSignature")&&d(g,h.PrettierIgnore))||e.match(void 0,(I,q)=>I.type==="TSImportType"&&q==="options")),M;if(B.length===0){if(!d(s,h.Dangling))return[c,A,H(e,r)];M=l([c,N(e,t,{indent:!0}),E,A,V(e),H(e,r)])}else M=[f&&O(s.properties)?oa(p):"",c,D([t.bracketSpacing?x:E,...B]),S(_&&(C!==","||ce(t))?C:""),t.bracketSpacing?x:E,A,V(e),H(e,r)];return e.match(I=>I.type==="ObjectPattern"&&!O(I.decorators),_s)||Re(s)&&(e.match(void 0,(I,q)=>q==="typeAnnotation",(I,q)=>q==="typeAnnotation",_s)||e.match(void 0,(I,q)=>I.type==="FunctionTypeParam"&&q==="typeAnnotation",_s))||!y&&e.match(I=>I.type==="ObjectPattern",I=>I.type==="AssignmentExpression"||I.type==="VariableDeclarator")?M:l(M,{shouldBreak:y})}function _s(e,t){return(t==="params"||t==="parameters"||t==="this"||t==="rest")&&gs(e)}function Zl(e){let t=[e];for(let r=0;r<t.length;r++){let n=t[r];for(let s of["test","consequent","alternate"]){let u=n[s];if(Y(u))return!0;u.type==="ConditionalExpression"&&t.push(u)}}return!1}function em(e,t,r){let{node:n}=e,s=n.type==="ConditionalExpression",u=s?"alternate":"falseType",{parent:i}=e,a=s?r("test"):[r("checkType")," ","extends"," ",r("extendsType")];return i.type===n.type&&i[u]===n?be(2,a):a}var tm=new Map([["AssignmentExpression","right"],["VariableDeclarator","init"],["ReturnStatement","argument"],["ThrowStatement","argument"],["UnaryExpression","argument"],["YieldExpression","argument"],["AwaitExpression","argument"]]);function rm(e){let{node:t}=e;if(t.type!=="ConditionalExpression")return!1;let r,n=t;for(let s=0;!r;s++){let u=e.getParentNode(s);if(u.type==="ChainExpression"&&u.expression===n||w(u)&&u.callee===n||G(u)&&u.object===n||u.type==="TSNonNullExpression"&&u.expression===n){n=u;continue}u.type==="NewExpression"&&u.callee===n||xe(u)&&u.expression===n?(r=e.getParentNode(s+1),n=u):r=u}return n===t?!1:r[tm.get(r.type)]===n}function ba(e,t,r){let{node:n}=e,s=n.type==="ConditionalExpression",u=s?"consequent":"trueType",i=s?"alternate":"falseType",a=s?["test"]:["checkType","extendsType"],o=n[u],p=n[i],m=[],f=!1,{parent:y}=e,C=y.type===n.type&&a.some(W=>y[W]===n),c=y.type===n.type&&!C,A,T,B=0;do T=A||n,A=e.getParentNode(B),B++;while(A&&A.type===n.type&&a.every(W=>A[W]!==T));let g=A||y,_=T;if(s&&(Y(n[a[0]])||Y(o)||Y(p)||Zl(_))){f=!0,c=!0;let W=Z=>[S("("),D([E,Z]),E,S(")")],ne=Z=>Z.type==="NullLiteral"||Z.type==="Literal"&&Z.value===null||Z.type==="Identifier"&&Z.name==="undefined";m.push(" ? ",ne(o)?r(u):W(r(u))," : ",p.type===n.type||ne(p)?r(i):W(r(i)))}else{let W=Z=>t.useTabs?D(r(Z)):be(2,r(Z)),ne=[x,"? ",o.type===n.type?S("","("):"",W(u),o.type===n.type?S("",")"):"",x,": ",W(i)];m.push(y.type!==n.type||y[i]===n||C?ne:t.useTabs?Jr(D(ne)):be(Math.max(0,t.tabWidth-2),ne))}let M=[u,i,...a].some(W=>d(n[W],ne=>te(ne)&&ie(t.originalText,j(ne),P(ne)))),J=W=>y===g?l(W,{shouldBreak:M}):M?[W,Ce]:W,I=!f&&(G(y)||y.type==="NGPipeExpression"&&y.left===n)&&!y.computed,q=rm(e),k=J([em(e,t,r),c?m:D(m),s&&I&&!q?E:""]);return C||q?l([D([E,k]),E]):k}function nm(e,t){return(G(t)||t.type==="NGPipeExpression"&&t.left===e)&&!t.computed}function sm(e,t,r,n){return[...e.map(u=>Ve(u)),Ve(t),Ve(r)].flat().some(u=>te(u)&&ie(n.originalText,j(u),P(u)))}var um=new Map([["AssignmentExpression","right"],["VariableDeclarator","init"],["ReturnStatement","argument"],["ThrowStatement","argument"],["UnaryExpression","argument"],["YieldExpression","argument"],["AwaitExpression","argument"]]);function im(e){let{node:t}=e;if(t.type!=="ConditionalExpression")return!1;let r,n=t;for(let s=0;!r;s++){let u=e.getParentNode(s);if(u.type==="ChainExpression"&&u.expression===n||w(u)&&u.callee===n||G(u)&&u.object===n||u.type==="TSNonNullExpression"&&u.expression===n){n=u;continue}u.type==="NewExpression"&&u.callee===n||xe(u)&&u.expression===n?(r=e.getParentNode(s+1),n=u):r=u}return n===t?!1:r[um.get(r.type)]===n}var Ms=e=>[S("("),D([E,e]),E,S(")")];function zt(e,t,r,n){if(!t.experimentalTernaries)return ba(e,t,r);let{node:s}=e,u=s.type==="ConditionalExpression",i=Je(s),a=u?"consequent":"trueType",o=u?"alternate":"falseType",p=u?["test"]:["checkType","extendsType"],m=s[a],f=s[o],y=p.map(Xe=>s[Xe]),{parent:C}=e,c=C.type===s.type,A=c&&p.some(Xe=>C[Xe]===s),T=c&&C[o]===s,B=m.type===s.type,g=f.type===s.type,_=g||T,M=t.tabWidth>2||t.useTabs,J,I,q=0;do I=J||s,J=e.getParentNode(q),q++;while(J&&J.type===s.type&&p.every(Xe=>J[Xe]!==I));let k=J||C,W=n&&n.assignmentLayout&&n.assignmentLayout!=="break-after-operator"&&(C.type==="AssignmentExpression"||C.type==="VariableDeclarator"||C.type==="ClassProperty"||C.type==="PropertyDefinition"||C.type==="ClassPrivateProperty"||C.type==="ObjectProperty"||C.type==="Property"),ne=(C.type==="ReturnStatement"||C.type==="ThrowStatement")&&!(B||g),Z=u&&k.type==="JSXExpressionContainer"&&e.grandparent.type!=="JSXAttribute",lt=im(e),L=nm(s,C),se=i&&Le(e,t),Ae=M?t.useTabs?"	":" ".repeat(t.tabWidth-1):"",Ot=sm(y,m,f,t)||B||g,nt=!_&&!c&&!i&&(Z?m.type==="NullLiteral"||m.type==="Literal"&&m.value===null:ir(m,t)&&Nn(s.test,3)),Js=_||T||i&&!c||c&&u&&Nn(s.test,1)||nt,Ns=[];!B&&d(m,h.Dangling)&&e.call(Xe=>{Ns.push(N(Xe,t),F)},"consequent");let er=[];d(s.test,h.Dangling)&&e.call(Xe=>{er.push(N(Xe,t))},"test"),!g&&d(f,h.Dangling)&&e.call(Xe=>{er.push(N(Xe,t))},"alternate"),d(s,h.Dangling)&&er.push(N(e,t));let Gs=Symbol("test"),Va=Symbol("consequent"),dr=Symbol("test-and-consequent"),$a=u?[Ms(r("test")),s.test.type==="ConditionalExpression"?Ce:""]:[r("checkType")," ","extends"," ",Je(s.extendsType)||s.extendsType.type==="TSMappedType"?r("extendsType"):l(Ms(r("extendsType")))],qs=l([$a," ?"],{id:Gs}),Ka=r(a),Tr=D([B||Z&&(Y(m)||c||_)?F:x,Ns,Ka]),Qa=Js?l([qs,_?Tr:S(Tr,l(Tr,{id:Va}),{groupId:Gs})],{id:dr}):[qs,Tr],wn=r(o),Ws=nt?S(wn,Jr(Ms(wn)),{groupId:dr}):wn,tr=[Qa,er.length>0?[D([F,er]),F]:g?F:nt?S(x," ",{groupId:dr}):x,":",g?" ":M?Js?S(Ae,S(_||nt?" ":Ae," "),{groupId:dr}):S(Ae," "):" ",g?Ws:l([D(Ws),Z&&!nt?E:""]),L&&!lt?E:"",Ot?Ce:""];return W&&!Ot?l(D([E,l(tr)])):W||ne?l(D(tr)):lt||i&&A?l([D([E,tr]),se?E:""]):C===k?l(tr):tr}function Pa(e,t,r,n){let{node:s}=e;if(wr(s))return Aa(e,t);let u=t.semi?";":"",i=[];switch(s.type){case"JsExpressionRoot":return r("node");case"JsonRoot":return[N(e,t),r("node"),F];case"File":return Ea(e,t,r)??r("program");case"EmptyStatement":return"";case"ExpressionStatement":return Da(e,t,r);case"ChainExpression":return r("expression");case"ParenthesizedExpression":return!d(s.expression)&&(ue(s.expression)||U(s.expression))?["(",r("expression"),")"]:l(["(",D([E,r("expression")]),E,")"]);case"AssignmentExpression":return Wi(e,t,r);case"VariableDeclarator":return Ui(e,t,r);case"BinaryExpression":case"LogicalExpression":return $r(e,t,r);case"AssignmentPattern":return[r("left")," = ",r("right")];case"OptionalMemberExpression":case"MemberExpression":return Ri(e,t,r);case"MetaProperty":return[r("meta"),".",r("property")];case"BindExpression":return s.object&&i.push(r("object")),i.push(l(D([E,Kr(e,t,r)]))),i;case"Identifier":return[s.name,V(e),mn(e),H(e,r)];case"V8IntrinsicIdentifier":return["%",s.name];case"SpreadElement":case"SpreadElementPattern":case"SpreadPropertyPattern":case"RestElement":return yn(e,r);case"FunctionDeclaration":case"FunctionExpression":return En(e,t,r,n);case"ArrowFunctionExpression":return sa(e,t,r,n);case"YieldExpression":return i.push("yield"),s.delegate&&i.push("*"),s.argument&&i.push(" ",r("argument")),i;case"AwaitExpression":if(i.push("await"),s.argument){i.push(" ",r("argument"));let{parent:a}=e;if(w(a)&&a.callee===s||G(a)&&a.object===s){i=[D([E,...i]),E];let o=e.findAncestor(p=>p.type==="AwaitExpression"||p.type==="BlockStatement");if((o==null?void 0:o.type)!=="AwaitExpression"||!pe(o.argument,p=>p===s))return l(i)}}return i;case"ExportDefaultDeclaration":case"ExportNamedDeclaration":case"ExportAllDeclaration":return Bn(e,t,r);case"ImportDeclaration":return da(e,t,r);case"ImportSpecifier":case"ExportSpecifier":case"ImportNamespaceSpecifier":case"ExportNamespaceSpecifier":case"ImportDefaultSpecifier":case"ExportDefaultSpecifier":return Ba(e,t,r);case"ImportAttribute":return Dn(e,t,r);case"Program":case"BlockStatement":case"StaticBlock":return An(e,t,r);case"ClassBody":return ca(e,t,r);case"ThrowStatement":return ra(e,t,r);case"ReturnStatement":return ta(e,t,r);case"NewExpression":case"ImportExpression":case"OptionalCallExpression":case"CallExpression":return Qr(e,t,r);case"ObjectExpression":case"ObjectPattern":return gt(e,t,r);case"Property":return bt(s)?Er(e,t,r):Dn(e,t,r);case"ObjectProperty":return Dn(e,t,r);case"ObjectMethod":return Er(e,t,r);case"Decorator":return["@",r("expression")];case"ArrayExpression":case"ArrayPattern":return Kt(e,t,r);case"SequenceExpression":{let{parent:a}=e;if(a.type==="ExpressionStatement"||a.type==="ForStatement"){let p=[];return e.each(({isFirst:m})=>{m?p.push(r()):p.push(",",D([x,r()]))},"expressions"),l(p)}let o=b([",",x],e.map(r,"expressions"));return(a.type==="ReturnStatement"||a.type==="ThrowStatement")&&e.key==="argument"||a.type==="ArrowFunctionExpression"&&e.key==="body"?l(S([D([E,o]),E],o)):l(o)}case"ThisExpression":return"this";case"Super":return"super";case"Directive":return[r("value"),u];case"UnaryExpression":return i.push(s.operator),/[a-z]$/u.test(s.operator)&&i.push(" "),d(s.argument)?i.push(l(["(",D([E,r("argument")]),E,")"])):i.push(r("argument")),i;case"UpdateExpression":return[s.prefix?s.operator:"",r("argument"),s.prefix?"":s.operator];case"ConditionalExpression":return zt(e,t,r,n);case"VariableDeclaration":{let a=e.map(r,"declarations"),o=e.parent,p=o.type==="ForStatement"||o.type==="ForInStatement"||o.type==="ForOfStatement",m=s.declarations.some(y=>y.init),f;return a.length===1&&!d(s.declarations[0])?f=a[0]:a.length>0&&(f=D(a[0])),i=[$(e),s.kind,f?[" ",f]:"",D(a.slice(1).map(y=>[",",m&&!p?F:x,y]))],p&&o.body!==s||i.push(u),l(i)}case"WithStatement":return l(["with (",r("object"),")",Et(s.body,r("body"))]);case"IfStatement":{let a=Et(s.consequent,r("consequent")),o=l(["if (",l([D([E,r("test")]),E]),")",a]);if(i.push(o),s.alternate){let p=d(s.consequent,h.Trailing|h.Line)||Mr(s),m=s.consequent.type==="BlockStatement"&&!p;i.push(m?" ":F),d(s,h.Dangling)&&i.push(N(e,t),p?F:" "),i.push("else",l(Et(s.alternate,r("alternate"),s.alternate.type==="IfStatement")))}return i}case"ForStatement":{let a=Et(s.body,r("body")),o=N(e,t),p=o?[o,E]:"";return!s.init&&!s.test&&!s.update?[p,l(["for (;;)",a])]:[p,l(["for (",l([D([E,r("init"),";",x,r("test"),";",x,r("update")]),E]),")",a])]}case"WhileStatement":return l(["while (",l([D([E,r("test")]),E]),")",Et(s.body,r("body"))]);case"ForInStatement":return l(["for (",r("left")," in ",r("right"),")",Et(s.body,r("body"))]);case"ForOfStatement":return l(["for",s.await?" await":""," (",r("left")," of ",r("right"),")",Et(s.body,r("body"))]);case"DoWhileStatement":{let a=Et(s.body,r("body"));return i=[l(["do",a])],s.body.type==="BlockStatement"?i.push(" "):i.push(F),i.push("while (",l([D([E,r("test")]),E]),")",u),i}case"DoExpression":return[s.async?"async ":"","do ",r("body")];case"BreakStatement":case"ContinueStatement":return i.push(s.type==="BreakStatement"?"break":"continue"),s.label&&i.push(" ",r("label")),i.push(u),i;case"LabeledStatement":return s.body.type==="EmptyStatement"?[r("label"),":;"]:[r("label"),": ",r("body")];case"TryStatement":return["try ",r("block"),s.handler?[" ",r("handler")]:"",s.finalizer?[" finally ",r("finalizer")]:""];case"CatchClause":if(s.param){let a=d(s.param,p=>!te(p)||p.leading&&ee(t.originalText,P(p))||p.trailing&&ee(t.originalText,j(p),{backwards:!0})),o=r("param");return["catch ",a?["(",D([E,o]),E,") "]:["(",o,") "],r("body")]}return["catch ",r("body")];case"SwitchStatement":return[l(["switch (",D([E,r("discriminant")]),E,")"])," {",s.cases.length>0?D([F,b(F,e.map(({node:a,isLast:o})=>[r(),!o&&me(a,t)?F:""],"cases"))]):"",F,"}"];case"SwitchCase":{s.test?i.push("case ",r("test"),":"):i.push("default:"),d(s,h.Dangling)&&i.push(" ",N(e,t));let a=s.consequent.filter(o=>o.type!=="EmptyStatement");if(a.length>0){let o=Fr(e,t,r,"consequent");i.push(a.length===1&&a[0].type==="BlockStatement"?[" ",o]:D([F,o]))}return i}case"DebuggerStatement":return["debugger",u];case"ClassDeclaration":case"ClassExpression":return xn(e,t,r);case"ClassMethod":case"ClassPrivateMethod":case"MethodDefinition":return hn(e,t,r);case"ClassProperty":case"PropertyDefinition":case"ClassPrivateProperty":case"ClassAccessorProperty":case"AccessorProperty":return gn(e,t,r);case"TemplateElement":return ve(s.value.raw);case"TemplateLiteral":return Wr(e,t,r);case"TaggedTemplateExpression":return Zu(e,t,r);case"PrivateIdentifier":return["#",s.name];case"PrivateName":return["#",r("id")];case"TopicReference":return"%";case"ArgumentPlaceholder":return"?";case"ModuleExpression":return["module ",r("body")];case"InterpreterDirective":default:throw new qe(s,"ESTree")}}function bn(e,t,r){let{parent:n,node:s,key:u}=e,i=[r("expression")];switch(s.type){case"AsConstExpression":i.push(" as const");break;case"AsExpression":case"TSAsExpression":i.push(" as ",r("typeAnnotation"));break;case"SatisfiesExpression":case"TSSatisfiesExpression":i.push(" satisfies ",r("typeAnnotation"));break}return u==="callee"&&w(n)||u==="object"&&G(n)?l([D([E,...i]),E]):i}function ka(e,t,r){let{node:n}=e,s=[$(e),"component"];n.id&&s.push(" ",r("id")),s.push(r("typeParameters"));let u=am(e,t,r);return n.rendersType?s.push(l([u," ",r("rendersType")])):s.push(l([u])),n.body&&s.push(" ",r("body")),t.semi&&n.type==="DeclareComponent"&&s.push(";"),s}function am(e,t,r){let{node:n}=e,s=n.params;if(n.rest&&(s=[...s,n.rest]),s.length===0)return["(",N(e,t,{filter:i=>ke(t.originalText,P(i))===")"}),")"];let u=[];return pm(e,(i,a)=>{let o=a===s.length-1;o&&n.rest&&u.push("..."),u.push(r()),!o&&(u.push(","),me(s[a],t)?u.push(F,F):u.push(x))}),["(",D([E,...u]),S(ce(t,"all")&&!om(n,s)?",":""),E,")"]}function om(e,t){var r;return e.rest||((r=v(!1,t,-1))==null?void 0:r.type)==="RestElement"}function pm(e,t){let{node:r}=e,n=0,s=u=>t(u,n++);e.each(s,"params"),r.rest&&e.call(s,"rest")}function Ia(e,t,r){let{node:n}=e;return n.shorthand?r("local"):[r("name")," as ",r("local")]}function La(e,t,r){let{node:n}=e,s=[];return n.name&&s.push(r("name"),n.optional?"?: ":": "),s.push(r("typeAnnotation")),s}function Pn(e,t,r){return gt(e,t,r)}function kn(e,t){let{node:r}=e,n=t("id");r.computed&&(n=["[",n,"]"]);let s="";return r.initializer&&(s=t("initializer")),r.init&&(s=t("init")),s?[n," = ",s]:n}function In(e,t){let{node:r}=e;return[$(e),r.const?"const ":"","enum ",t("id")," ",t("body")]}function Oa(e,t,r){let{node:n}=e,s=["hook"];n.id&&s.push(" ",r("id"));let u=Ue(e,t,r,!1,!0),i=Qt(e,r),a=ct(n,i);return s.push(l([a?l(u):u,i]),n.body?" ":"",r("body")),s}function _a(e,t,r){let{node:n}=e,s=[$(e),"hook"];return n.id&&s.push(" ",r("id")),t.semi&&s.push(";"),s}function wa(e){var r;let{node:t}=e;return t.type==="HookTypeAnnotation"&&((r=e.getParentNode(2))==null?void 0:r.type)==="DeclareHook"}function Ma(e,t,r){let{node:n}=e,s=[];s.push(wa(e)?"":"hook ");let u=Ue(e,t,r,!1,!0),i=[];return i.push(wa(e)?": ":" => ",r("returnType")),ct(n,i)&&(u=l(u)),s.push(u,i),l(s)}function Ln(e,t,r){let{node:n}=e,s=[$(e),"interface"],u=[],i=[];n.type!=="InterfaceTypeAnnotation"&&u.push(" ",r("id"),r("typeParameters"));let a=n.typeParameters&&!d(n.typeParameters,h.Trailing|h.Line);return O(n.extends)&&i.push(a?S(" ",x,{groupId:Cr(n.typeParameters)}):x,"extends ",(n.extends.length===1?du:D)(b([",",x],e.map(r,"extends")))),d(n.id,h.Trailing)||O(n.extends)?a?s.push(l([...u,D(i)])):s.push(l(D([...u,...i]))):s.push(...u,...i),s.push(" ",r("body")),l(s)}function cm(e){switch(e){case null:return"";case"PlusOptional":return"+?";case"MinusOptional":return"-?";case"Optional":return"?"}}function ja(e,t,r){let{node:n}=e;return l([n.variance?r("variance"):"","[",D([r("keyTparam")," in ",r("sourceType")]),"]",cm(n.optional),": ",r("propType")])}function va(e,t){return e==="+"||e==="-"?e+t:t}function Ra(e,t,r){let{node:n}=e,s=!1;if(t.objectWrap==="preserve"){let u=j(n),i=Ar(t,u+1,j(n.key)),a=u+1+i.search(/\S/u);ie(t.originalText,u,a)&&(s=!0)}return l(["{",D([t.bracketSpacing?x:E,d(n,h.Dangling)?l([N(e,t),F]):"",l([n.readonly?[va(n.readonly,"readonly")," "]:"","[",r("key")," in ",r("constraint"),n.nameType?[" as ",r("nameType")]:"","]",n.optional?va(n.optional,"?"):"",n.typeAnnotation?": ":"",r("typeAnnotation")]),t.semi?S(";"):""]),t.bracketSpacing?x:E,"}"],{shouldBreak:s})}function Ja(e,t,r){let{node:n}=e;if(Pr(n))return n.type.slice(0,-14).toLowerCase();let s=t.semi?";":"";switch(n.type){case"ComponentDeclaration":case"DeclareComponent":case"ComponentTypeAnnotation":return ka(e,t,r);case"ComponentParameter":return Ia(e,t,r);case"ComponentTypeParameter":return La(e,t,r);case"HookDeclaration":return Oa(e,t,r);case"DeclareHook":return _a(e,t,r);case"HookTypeAnnotation":return Ma(e,t,r);case"DeclareClass":return xn(e,t,r);case"DeclareFunction":return[$(e),"function ",r("id"),r("predicate"),s];case"DeclareModule":return["declare module ",r("id")," ",r("body")];case"DeclareModuleExports":return["declare module.exports",H(e,r),s];case"DeclareNamespace":return["declare namespace ",r("id")," ",r("body")];case"DeclareVariable":return[$(e),n.kind??"var"," ",r("id"),s];case"DeclareExportDeclaration":case"DeclareExportAllDeclaration":return Bn(e,t,r);case"DeclareOpaqueType":case"OpaqueType":return Vi(e,t,r);case"DeclareTypeAlias":case"TypeAlias":return Zr(e,t,r);case"IntersectionTypeAnnotation":return en(e,t,r);case"UnionTypeAnnotation":return tn(e,t,r);case"ConditionalTypeAnnotation":return zt(e,t,r);case"InferTypeAnnotation":return sn(e,t,r);case"FunctionTypeAnnotation":return rn(e,t,r);case"TupleTypeAnnotation":return Kt(e,t,r);case"TupleTypeLabeledElement":return an(e,t,r);case"TupleTypeSpreadElement":return un(e,t,r);case"GenericTypeAnnotation":return[r("id"),wt(e,t,r,"typeParameters")];case"IndexedAccessType":case"OptionalIndexedAccessType":return nn(e,t,r);case"TypeAnnotation":return on(e,t,r);case"TypeParameter":return Tn(e,t,r);case"TypeofTypeAnnotation":return cn(e,r);case"ExistsTypeAnnotation":return"*";case"ArrayTypeAnnotation":return pn(r);case"DeclareEnum":case"EnumDeclaration":return In(e,r);case"EnumBooleanBody":case"EnumNumberBody":case"EnumBigIntBody":case"EnumStringBody":case"EnumSymbolBody":return[n.type==="EnumSymbolBody"||n.explicitType?`of ${n.type.slice(4,-4).toLowerCase()} `:"",Pn(e,t,r)];case"EnumBooleanMember":case"EnumNumberMember":case"EnumBigIntMember":case"EnumStringMember":case"EnumDefaultedMember":return kn(e,r);case"FunctionTypeParam":{let u=n.name?r("name"):e.parent.this===n?"this":"";return[u,V(e),u?": ":"",r("typeAnnotation")]}case"DeclareInterface":case"InterfaceDeclaration":case"InterfaceTypeAnnotation":return Ln(e,t,r);case"ClassImplements":case"InterfaceExtends":return[r("id"),r("typeParameters")];case"NullableTypeAnnotation":return["?",r("typeAnnotation")];case"Variance":{let{kind:u}=n;return jt.ok(u==="plus"||u==="minus"),u==="plus"?"+":"-"}case"KeyofTypeAnnotation":return["keyof ",r("argument")];case"ObjectTypeCallProperty":return[n.static?"static ":"",r("value")];case"ObjectTypeMappedTypeProperty":return ja(e,t,r);case"ObjectTypeIndexer":return[n.static?"static ":"",n.variance?r("variance"):"","[",r("id"),n.id?": ":"",r("key"),"]: ",r("value")];case"ObjectTypeProperty":{let u="";return n.proto?u="proto ":n.static&&(u="static "),[u,n.kind!=="init"?n.kind+" ":"",n.variance?r("variance"):"",Ct(e,t,r),V(e),bt(n)?"":": ",r("value")]}case"ObjectTypeAnnotation":return gt(e,t,r);case"ObjectTypeInternalSlot":return[n.static?"static ":"","[[",r("id"),"]]",V(e),n.method?"":": ",r("value")];case"ObjectTypeSpreadProperty":return yn(e,r);case"QualifiedTypeofIdentifier":case"QualifiedTypeIdentifier":return[r("qualification"),".",r("id")];case"NullLiteralTypeAnnotation":return"null";case"BooleanLiteralTypeAnnotation":return String(n.value);case"StringLiteralTypeAnnotation":return ve(ut(ae(n),t));case"NumberLiteralTypeAnnotation":return Ft(ae(n));case"BigIntLiteralTypeAnnotation":return Sn(ae(n));case"TypeCastExpression":return["(",r("expression"),H(e,r),")"];case"TypePredicate":return ln(e,r);case"TypeOperator":return[n.operator," ",r("typeAnnotation")];case"TypeParameterDeclaration":case"TypeParameterInstantiation":return wt(e,t,r,"params");case"InferredPredicate":case"DeclaredPredicate":return[e.key==="predicate"&&e.parent.type!=="DeclareFunction"&&!e.parent.returnType?": ":" ","%checks",...n.type==="DeclaredPredicate"?["(",r("value"),")"]:[]];case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":return bn(e,t,r)}}function Na(e,t,r){var i;let{node:n}=e;if(!n.type.startsWith("TS"))return;if(kr(n))return n.type.slice(2,-7).toLowerCase();let s=t.semi?";":"",u=[];switch(n.type){case"TSThisType":return"this";case"TSTypeAssertion":{let a=!(U(n.expression)||ue(n.expression)),o=l(["<",D([E,r("typeAnnotation")]),E,">"]),p=[S("("),D([E,r("expression")]),E,S(")")];return a?tt([[o,r("expression")],[o,l(p,{shouldBreak:!0})],[o,r("expression")]]):l([o,r("expression")])}case"TSDeclareFunction":return En(e,t,r);case"TSExportAssignment":return["export = ",r("expression"),s];case"TSModuleBlock":return An(e,t,r);case"TSInterfaceBody":case"TSTypeLiteral":return gt(e,t,r);case"TSTypeAliasDeclaration":return Zr(e,t,r);case"TSQualifiedName":return[r("left"),".",r("right")];case"TSAbstractMethodDefinition":case"TSDeclareMethod":return hn(e,t,r);case"TSAbstractAccessorProperty":case"TSAbstractPropertyDefinition":return gn(e,t,r);case"TSInterfaceHeritage":case"TSClassImplements":case"TSExpressionWithTypeArguments":case"TSInstantiationExpression":return[r("expression"),r(n.typeArguments?"typeArguments":"typeParameters")];case"TSTemplateLiteralType":return Wr(e,t,r);case"TSNamedTupleMember":return an(e,t,r);case"TSRestType":return un(e,t,r);case"TSOptionalType":return[r("typeAnnotation"),"?"];case"TSInterfaceDeclaration":return Ln(e,t,r);case"TSTypeParameterDeclaration":case"TSTypeParameterInstantiation":return wt(e,t,r,"params");case"TSTypeParameter":return Tn(e,t,r);case"TSAsExpression":case"TSSatisfiesExpression":return bn(e,t,r);case"TSArrayType":return pn(r);case"TSPropertySignature":return[n.readonly?"readonly ":"",Ct(e,t,r),V(e),H(e,r)];case"TSParameterProperty":return[$t(n),n.static?"static ":"",n.override?"override ":"",n.readonly?"readonly ":"",r("parameter")];case"TSTypeQuery":return cn(e,r);case"TSIndexSignature":{let a=n.parameters.length>1?S(ce(t)?",":""):"",o=l([D([E,b([", ",E],e.map(r,"parameters"))]),a,E]),p=e.parent.type==="ClassBody"&&e.key==="body";return[p&&n.static?"static ":"",n.readonly?"readonly ":"","[",n.parameters?o:"","]",H(e,r),p?s:""]}case"TSTypePredicate":return ln(e,r);case"TSNonNullExpression":return[r("expression"),"!"];case"TSImportType":return["import",Lt(e,t,r),n.qualifier?[".",r("qualifier")]:"",wt(e,t,r,n.typeArguments?"typeArguments":"typeParameters")];case"TSLiteralType":return r("literal");case"TSIndexedAccessType":return nn(e,t,r);case"TSTypeOperator":return[n.operator," ",r("typeAnnotation")];case"TSMappedType":return Ra(e,t,r);case"TSMethodSignature":{let a=n.kind&&n.kind!=="method"?`${n.kind} `:"";u.push($t(n),a,n.computed?"[":"",r("key"),n.computed?"]":"",V(e));let o=Ue(e,t,r,!1,!0),p=n.returnType?"returnType":"typeAnnotation",m=n[p],f=m?H(e,r,p):"",y=ct(n,f);return u.push(y?l(o):o),m&&u.push(l(f)),l(u)}case"TSNamespaceExportDeclaration":return["export as namespace ",r("id"),t.semi?";":""];case"TSEnumDeclaration":return In(e,r);case"TSEnumBody":return Pn(e,t,r);case"TSEnumMember":return kn(e,r);case"TSImportEqualsDeclaration":return[n.isExport?"export ":"","import ",Os(n,!1),r("id")," = ",r("moduleReference"),t.semi?";":""];case"TSExternalModuleReference":return["require(",r("expression"),")"];case"TSModuleDeclaration":{let{parent:a}=e,o=a.type==="TSModuleDeclaration",p=((i=n.body)==null?void 0:i.type)==="TSModuleDeclaration";return o?u.push("."):(u.push($(e)),n.kind!=="global"&&u.push(n.kind," ")),u.push(r("id")),p?u.push(r("body")):n.body?u.push(" ",l(r("body"))):u.push(s),u}case"TSConditionalType":return zt(e,t,r);case"TSInferType":return sn(e,t,r);case"TSIntersectionType":return en(e,t,r);case"TSUnionType":return tn(e,t,r);case"TSFunctionType":case"TSCallSignatureDeclaration":case"TSConstructorType":case"TSConstructSignatureDeclaration":return rn(e,t,r);case"TSTupleType":return Kt(e,t,r);case"TSTypeReference":return[r("typeName"),wt(e,t,r,n.typeArguments?"typeArguments":"typeParameters")];case"TSTypeAnnotation":return on(e,t,r);case"TSEmptyBodyFunctionExpression":return Fn(e,t,r);case"TSJSDocAllType":return"*";case"TSJSDocUnknownType":return"?";case"TSJSDocNullableType":return Bs(e,r,"?");case"TSJSDocNonNullableType":return Bs(e,r,"!");case"TSParenthesizedType":default:throw new qe(n,"TypeScript")}}function lm(e,t,r,n){if(Vr(e))return Ti(e,t);for(let s of[Li,bi,Ja,Na,Pa]){let u=s(e,t,r,n);if(u!==void 0)return u}}var mm=R(["ClassMethod","ClassPrivateMethod","ClassProperty","ClassAccessorProperty","AccessorProperty","TSAbstractAccessorProperty","PropertyDefinition","TSAbstractPropertyDefinition","ClassPrivateProperty","MethodDefinition","TSAbstractMethodDefinition","TSDeclareMethod"]);function ym(e,t,r,n){var f;e.isRoot&&((f=t.__onHtmlBindingRoot)==null||f.call(t,e.node,t));let s=lm(e,t,r,n);if(!s)return"";let{node:u}=e;if(mm(u))return s;let i=O(u.decorators),a=Oi(e,t,r),o=u.type==="ClassExpression";if(i&&!o)return lr(s,y=>l([a,y]));let p=Le(e,t),m=ya(e,t);return!a&&!p&&!m?s:lr(s,y=>[m?";":"",p?"(":"",p&&o&&i?[D([x,a,y]),x]:[a,y],p?")":""])}var Ga=ym;var fm={avoidAstMutation:!0};var qa=[{name:"JSON.stringify",type:"data",extensions:[".importmap"],tmScope:"source.json",aceMode:"json",aliases:["geojson","jsonl","sarif","topojson"],codemirrorMode:"javascript",codemirrorMimeType:"application/json",filenames:["package.json","package-lock.json","composer.json"],parsers:["json-stringify"],vscodeLanguageIds:["json"],linguistLanguageId:174},{name:"JSON",type:"data",extensions:[".json",".4DForm",".4DProject",".avsc",".geojson",".gltf",".har",".ice",".JSON-tmLanguage",".json.example",".mcmeta",".sarif",".tact",".tfstate",".tfstate.backup",".topojson",".webapp",".webmanifest",".yy",".yyp"],tmScope:"source.json",aceMode:"json",aliases:["geojson","jsonl","sarif","topojson"],codemirrorMode:"javascript",codemirrorMimeType:"application/json",filenames:[".all-contributorsrc",".arcconfig",".auto-changelog",".c8rc",".htmlhintrc",".imgbotconfig",".nycrc",".tern-config",".tern-project",".watchmanconfig",".babelrc",".jscsrc",".jshintrc",".jslintrc",".swcrc"],parsers:["json"],vscodeLanguageIds:["json"],linguistLanguageId:174},{name:"JSON with Comments",type:"data",extensions:[".jsonc",".code-snippets",".code-workspace",".sublime-build",".sublime-color-scheme",".sublime-commands",".sublime-completions",".sublime-keymap",".sublime-macro",".sublime-menu",".sublime-mousemap",".sublime-project",".sublime-settings",".sublime-theme",".sublime-workspace",".sublime_metrics",".sublime_session"],tmScope:"source.json.comments",aceMode:"javascript",aliases:["jsonc"],codemirrorMode:"javascript",codemirrorMimeType:"text/javascript",group:"JSON",filenames:[],parsers:["jsonc"],vscodeLanguageIds:["jsonc"],linguistLanguageId:423},{name:"JSON5",type:"data",extensions:[".json5"],tmScope:"source.js",aceMode:"javascript",codemirrorMode:"javascript",codemirrorMimeType:"application/json",parsers:["json5"],vscodeLanguageIds:["json5"],linguistLanguageId:175}];var js={};xr(js,{getVisitorKeys:()=>Ua,massageAstNode:()=>Ya,print:()=>Fm});var Dm={JsonRoot:["node"],ArrayExpression:["elements"],ObjectExpression:["properties"],ObjectProperty:["key","value"],UnaryExpression:["argument"],NullLiteral:[],BooleanLiteral:[],StringLiteral:[],NumericLiteral:[],Identifier:[],TemplateLiteral:["quasis"],TemplateElement:[]},Wa=Dm;var Em=Br(Wa),Ua=Em;function Fm(e,t,r){let{node:n}=e;switch(n.type){case"JsonRoot":return[r("node"),F];case"ArrayExpression":{if(n.elements.length===0)return"[]";let s=e.map(()=>e.node===null?"null":r(),"elements");return["[",D([F,b([",",F],s)]),F,"]"]}case"ObjectExpression":return n.properties.length===0?"{}":["{",D([F,b([",",F],e.map(r,"properties"))]),F,"}"];case"ObjectProperty":return[r("key"),": ",r("value")];case"UnaryExpression":return[n.operator==="+"?"":n.operator,r("argument")];case"NullLiteral":return"null";case"BooleanLiteral":return n.value?"true":"false";case"StringLiteral":return JSON.stringify(n.value);case"NumericLiteral":return Xa(e)?JSON.stringify(String(n.value)):JSON.stringify(n.value);case"Identifier":return Xa(e)?JSON.stringify(n.name):n.name;case"TemplateLiteral":return r(["quasis",0]);case"TemplateElement":return JSON.stringify(n.value.cooked);default:throw new qe(n,"JSON")}}function Xa(e){return e.key==="key"&&e.parent.type==="ObjectProperty"}var Cm=new Set(["start","end","extra","loc","comments","leadingComments","trailingComments","innerComments","errors","range","tokens"]);function Ya(e,t){let{type:r}=e;if(r==="ObjectProperty"){let{key:n}=e;n.type==="Identifier"?t.key={type:"StringLiteral",value:n.name}:n.type==="NumericLiteral"&&(t.key={type:"StringLiteral",value:String(n.value)});return}if(r==="UnaryExpression"&&e.operator==="+")return t.argument;if(r==="ArrayExpression"){for(let[n,s]of e.elements.entries())s===null&&t.elements.splice(n,0,{type:"NullLiteral"});return}if(r==="TemplateLiteral")return{type:"StringLiteral",value:e.quasis[0].value.cooked}}Ya.ignoredProperties=Cm;var Zt={bracketSpacing:{category:"Common",type:"boolean",default:!0,description:"Print spaces between brackets.",oppositeDescription:"Do not print spaces between brackets."},objectWrap:{category:"Common",type:"choice",default:"preserve",description:"How to wrap object literals.",choices:[{value:"preserve",description:"Keep as multi-line, if there is a newline between the opening brace and first property."},{value:"collapse",description:"Fit to a single line when possible."}]},singleQuote:{category:"Common",type:"boolean",default:!1,description:"Use single quotes instead of double quotes."},proseWrap:{category:"Common",type:"choice",default:"preserve",description:"How to wrap prose.",choices:[{value:"always",description:"Wrap prose if it exceeds the print width."},{value:"never",description:"Do not wrap prose."},{value:"preserve",description:"Wrap prose as-is."}]},bracketSameLine:{category:"Common",type:"boolean",default:!1,description:"Put > of opening tags on the last line instead of on a new line."},singleAttributePerLine:{category:"Common",type:"boolean",default:!1,description:"Enforce single attribute per line in HTML, Vue and JSX."}};var St="JavaScript",Am={arrowParens:{category:St,type:"choice",default:"always",description:"Include parentheses around a sole arrow function parameter.",choices:[{value:"always",description:"Always include parens. Example: `(x) => x`"},{value:"avoid",description:"Omit parens when possible. Example: `x => x`"}]},bracketSameLine:Zt.bracketSameLine,objectWrap:Zt.objectWrap,bracketSpacing:Zt.bracketSpacing,jsxBracketSameLine:{category:St,type:"boolean",description:"Put > on the last line instead of at a new line.",deprecated:"2.4.0"},semi:{category:St,type:"boolean",default:!0,description:"Print semicolons.",oppositeDescription:"Do not print semicolons, except at the beginning of lines which may need them."},experimentalOperatorPosition:{category:St,type:"choice",default:"end",description:"Where to print operators when binary expressions wrap lines.",choices:[{value:"start",description:"Print operators at the start of new lines."},{value:"end",description:"Print operators at the end of previous lines."}]},experimentalTernaries:{category:St,type:"boolean",default:!1,description:"Use curious ternaries, with the question mark after the condition.",oppositeDescription:"Default behavior of ternaries; keep question marks on the same line as the consequent."},singleQuote:Zt.singleQuote,jsxSingleQuote:{category:St,type:"boolean",default:!1,description:"Use single quotes in JSX."},quoteProps:{category:St,type:"choice",default:"as-needed",description:"Change when properties in objects are quoted.",choices:[{value:"as-needed",description:"Only add quotes around object properties where required."},{value:"consistent",description:"If at least one property in an object requires quotes, quote all properties."},{value:"preserve",description:"Respect the input use of quotes in object properties."}]},trailingComma:{category:St,type:"choice",default:"all",description:"Print trailing commas wherever possible when multi-line.",choices:[{value:"all",description:"Trailing commas wherever possible (including function arguments)."},{value:"es5",description:"Trailing commas where valid in ES5 (objects, arrays, etc.)"},{value:"none",description:"No trailing commas."}]},singleAttributePerLine:Zt.singleAttributePerLine},Ha=Am;var dm={estree:vs,"estree-json":js},Tm=[...Vs,...qa];var Nx=Rs;export{Nx as default,Tm as languages,Ha as options,dm as printers};
