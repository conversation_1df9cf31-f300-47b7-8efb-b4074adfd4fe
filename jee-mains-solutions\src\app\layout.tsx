import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from '@/components/providers/AuthProvider';
import { Navigation } from '@/components/layout/Navigation';
// import { GoogleAnalytics } from '@/components/analytics/GoogleAnalytics';

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "JEE Mains Solutions - Question-wise Solutions & Video Explanations",
  description: "Comprehensive JEE Mains question-wise solutions with step-by-step explanations and video tutorials for Physics, Chemistry, and Mathematics.",
  keywords: "JEE Mains, solutions, physics, chemistry, mathematics, question bank, video solutions",
  authors: [{ name: "JEE Mains Solutions Team" }],
  openGraph: {
    title: "JEE Mains Solutions",
    description: "Question-wise solutions with video explanations",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <AuthProvider>
          {/* <GoogleAnalytics /> */}
          <div className="relative flex min-h-screen flex-col">
            <Navigation />
            <main className="flex-1">
              {children}
            </main>
          </div>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'var(--background)',
                color: 'var(--foreground)',
                border: '1px solid var(--border)',
              },
            }}
          />
        </AuthProvider>
      </body>
    </html>
  );
}
