(function(n){function e(){var i=n();return i.default||i}if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var t=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};t.prettierPlugins=t.prettierPlugins||{},t.prettierPlugins.angular=e()}})(function(){"use strict";var dt=Object.defineProperty;var qs=Object.getOwnPropertyDescriptor;var js=Object.getOwnPropertyNames;var zs=Object.prototype.hasOwnProperty;var tn=t=>{throw TypeError(t)};var nn=(t,e)=>{for(var n in e)dt(t,n,{get:e[n],enumerable:!0})},Gs=(t,e,n,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of js(e))!zs.call(t,r)&&r!==n&&dt(t,r,{get:()=>e[r],enumerable:!(s=qs(e,r))||s.enumerable});return t};var Xs=t=>Gs(dt({},"__esModule",{value:!0}),t);var mt=(t,e,n)=>e.has(t)||tn("Cannot "+n);var L=(t,e,n)=>(mt(t,e,"read from private field"),n?n.call(t):e.get(t)),V=(t,e,n)=>e.has(t)?tn("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),te=(t,e,n,s)=>(mt(t,e,"write to private field"),s?s.call(t,n):e.set(t,n),n),c=(t,e,n)=>(mt(t,e,"access private method"),n);var si={};nn(si,{parsers:()=>Zt});var Zt={};nn(Zt,{__ng_action:()=>Zr,__ng_binding:()=>ei,__ng_directive:()=>ni,__ng_interpolation:()=>ti});var ii=new RegExp(`(\\:not\\()|(([\\.\\#]?)[-\\w]+)|(?:\\[([-.\\w*\\\\$]+)(?:=(["']?)([^\\]"']*)\\5)?\\])|(\\))|(\\s*,\\s*)`,"g");var sn;(function(t){t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom"})(sn||(sn={}));var rn;(function(t){t[t.OnPush=0]="OnPush",t[t.Default=1]="Default"})(rn||(rn={}));var on;(function(t){t[t.None=0]="None",t[t.SignalBased=1]="SignalBased",t[t.HasDecoratorInputTransform=2]="HasDecoratorInputTransform"})(on||(on={}));var D;(function(t){t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL"})(D||(D={}));var an;(function(t){t[t.Error=0]="Error",t[t.Warning=1]="Warning",t[t.Ignore=2]="Ignore"})(an||(an={}));var ln;(function(t){t[t.Directive=0]="Directive",t[t.Component=1]="Component",t[t.Injectable=2]="Injectable",t[t.Pipe=3]="Pipe",t[t.NgModule=4]="NgModule"})(ln||(ln={}));var cn;(function(t){t[t.Directive=0]="Directive",t[t.Pipe=1]="Pipe",t[t.NgModule=2]="NgModule"})(cn||(cn={}));var un;(function(t){t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom"})(un||(un={}));var pn;(function(t){t[t.Little=0]="Little",t[t.Big=1]="Big"})(pn||(pn={}));var hn;(function(t){t[t.None=0]="None",t[t.Const=1]="Const"})(hn||(hn={}));var fn;(function(t){t[t.Dynamic=0]="Dynamic",t[t.Bool=1]="Bool",t[t.String=2]="String",t[t.Int=3]="Int",t[t.Number=4]="Number",t[t.Function=5]="Function",t[t.Inferred=6]="Inferred",t[t.None=7]="None"})(fn||(fn={}));var Js=void 0;var dn;(function(t){t[t.Minus=0]="Minus",t[t.Plus=1]="Plus"})(dn||(dn={}));var _;(function(t){t[t.Equals=0]="Equals",t[t.NotEquals=1]="NotEquals",t[t.Identical=2]="Identical",t[t.NotIdentical=3]="NotIdentical",t[t.Minus=4]="Minus",t[t.Plus=5]="Plus",t[t.Divide=6]="Divide",t[t.Multiply=7]="Multiply",t[t.Modulo=8]="Modulo",t[t.And=9]="And",t[t.Or=10]="Or",t[t.BitwiseOr=11]="BitwiseOr",t[t.BitwiseAnd=12]="BitwiseAnd",t[t.Lower=13]="Lower",t[t.LowerEquals=14]="LowerEquals",t[t.Bigger=15]="Bigger",t[t.BiggerEquals=16]="BiggerEquals",t[t.NullishCoalesce=17]="NullishCoalesce",t[t.Exponentiation=18]="Exponentiation",t[t.In=19]="In"})(_||(_={}));function Ys(t,e){return t==null||e==null?t==e:t.isEquivalent(e)}function Qs(t,e,n){let s=t.length;if(s!==e.length)return!1;for(let r=0;r<s;r++)if(!n(t[r],e[r]))return!1;return!0}function ct(t,e){return Qs(t,e,(n,s)=>n.isEquivalent(s))}var b=class{type;sourceSpan;constructor(e,n){this.type=e||null,this.sourceSpan=n||null}prop(e,n){return new Ct(this,e,null,n)}key(e,n,s){return new Tt(this,e,n,s)}callFn(e,n,s){return new St(this,e,null,n,s)}instantiate(e,n,s){return new Et(this,e,n,s)}conditional(e,n=null,s){return new _t(this,e,n,null,s)}equals(e,n){return new C(_.Equals,this,e,null,n)}notEquals(e,n){return new C(_.NotEquals,this,e,null,n)}identical(e,n){return new C(_.Identical,this,e,null,n)}notIdentical(e,n){return new C(_.NotIdentical,this,e,null,n)}minus(e,n){return new C(_.Minus,this,e,null,n)}plus(e,n){return new C(_.Plus,this,e,null,n)}divide(e,n){return new C(_.Divide,this,e,null,n)}multiply(e,n){return new C(_.Multiply,this,e,null,n)}modulo(e,n){return new C(_.Modulo,this,e,null,n)}power(e,n){return new C(_.Exponentiation,this,e,null,n)}and(e,n){return new C(_.And,this,e,null,n)}bitwiseOr(e,n){return new C(_.BitwiseOr,this,e,null,n)}bitwiseAnd(e,n){return new C(_.BitwiseAnd,this,e,null,n)}or(e,n){return new C(_.Or,this,e,null,n)}lower(e,n){return new C(_.Lower,this,e,null,n)}lowerEquals(e,n){return new C(_.LowerEquals,this,e,null,n)}bigger(e,n){return new C(_.Bigger,this,e,null,n)}biggerEquals(e,n){return new C(_.BiggerEquals,this,e,null,n)}isBlank(e){return this.equals(TYPED_NULL_EXPR,e)}nullishCoalesce(e,n){return new C(_.NullishCoalesce,this,e,null,n)}toStmt(){return new Nt(this,null)}},st=class t extends b{name;constructor(e,n,s){super(n,s),this.name=e}isEquivalent(e){return e instanceof t&&this.name===e.name}isConstant(){return!1}visitExpression(e,n){return e.visitReadVarExpr(this,n)}clone(){return new t(this.name,this.type,this.sourceSpan)}set(e){return new vt(this.name,e,null,this.sourceSpan)}},gt=class t extends b{expr;constructor(e,n,s){super(n,s),this.expr=e}visitExpression(e,n){return e.visitTypeofExpr(this,n)}isEquivalent(e){return e instanceof t&&e.expr.isEquivalent(this.expr)}isConstant(){return this.expr.isConstant()}clone(){return new t(this.expr.clone())}};var vt=class t extends b{name;value;constructor(e,n,s,r){super(s||n.type,r),this.name=e,this.value=n}isEquivalent(e){return e instanceof t&&this.name===e.name&&this.value.isEquivalent(e.value)}isConstant(){return!1}visitExpression(e,n){return e.visitWriteVarExpr(this,n)}clone(){return new t(this.name,this.value.clone(),this.type,this.sourceSpan)}toDeclStmt(e,n){return new It(this.name,this.value,e,n,this.sourceSpan)}toConstDecl(){return this.toDeclStmt(Js,Ae.Final)}},wt=class t extends b{receiver;index;value;constructor(e,n,s,r,i){super(r||s.type,i),this.receiver=e,this.index=n,this.value=s}isEquivalent(e){return e instanceof t&&this.receiver.isEquivalent(e.receiver)&&this.index.isEquivalent(e.index)&&this.value.isEquivalent(e.value)}isConstant(){return!1}visitExpression(e,n){return e.visitWriteKeyExpr(this,n)}clone(){return new t(this.receiver.clone(),this.index.clone(),this.value.clone(),this.type,this.sourceSpan)}},xt=class t extends b{receiver;name;value;constructor(e,n,s,r,i){super(r||s.type,i),this.receiver=e,this.name=n,this.value=s}isEquivalent(e){return e instanceof t&&this.receiver.isEquivalent(e.receiver)&&this.name===e.name&&this.value.isEquivalent(e.value)}isConstant(){return!1}visitExpression(e,n){return e.visitWritePropExpr(this,n)}clone(){return new t(this.receiver.clone(),this.name,this.value.clone(),this.type,this.sourceSpan)}},St=class t extends b{fn;args;pure;constructor(e,n,s,r,i=!1){super(s,r),this.fn=e,this.args=n,this.pure=i}get receiver(){return this.fn}isEquivalent(e){return e instanceof t&&this.fn.isEquivalent(e.fn)&&ct(this.args,e.args)&&this.pure===e.pure}isConstant(){return!1}visitExpression(e,n){return e.visitInvokeFunctionExpr(this,n)}clone(){return new t(this.fn.clone(),this.args.map(e=>e.clone()),this.type,this.sourceSpan,this.pure)}};var Et=class t extends b{classExpr;args;constructor(e,n,s,r){super(s,r),this.classExpr=e,this.args=n}isEquivalent(e){return e instanceof t&&this.classExpr.isEquivalent(e.classExpr)&&ct(this.args,e.args)}isConstant(){return!1}visitExpression(e,n){return e.visitInstantiateExpr(this,n)}clone(){return new t(this.classExpr.clone(),this.args.map(e=>e.clone()),this.type,this.sourceSpan)}},rt=class t extends b{value;constructor(e,n,s){super(n,s),this.value=e}isEquivalent(e){return e instanceof t&&this.value===e.value}isConstant(){return!0}visitExpression(e,n){return e.visitLiteralExpr(this,n)}clone(){return new t(this.value,this.type,this.sourceSpan)}};var yt=class t extends b{value;typeParams;constructor(e,n,s=null,r){super(n,r),this.value=e,this.typeParams=s}isEquivalent(e){return e instanceof t&&this.value.name===e.value.name&&this.value.moduleName===e.value.moduleName}isConstant(){return!1}visitExpression(e,n){return e.visitExternalExpr(this,n)}clone(){return new t(this.value,this.type,this.typeParams,this.sourceSpan)}};var _t=class t extends b{condition;falseCase;trueCase;constructor(e,n,s=null,r,i){super(r||n.type,i),this.condition=e,this.falseCase=s,this.trueCase=n}isEquivalent(e){return e instanceof t&&this.condition.isEquivalent(e.condition)&&this.trueCase.isEquivalent(e.trueCase)&&Ys(this.falseCase,e.falseCase)}isConstant(){return!1}visitExpression(e,n){return e.visitConditionalExpr(this,n)}clone(){var e;return new t(this.condition.clone(),this.trueCase.clone(),(e=this.falseCase)==null?void 0:e.clone(),this.type,this.sourceSpan)}};var C=class t extends b{operator;rhs;lhs;constructor(e,n,s,r,i){super(r||n.type,i),this.operator=e,this.rhs=s,this.lhs=n}isEquivalent(e){return e instanceof t&&this.operator===e.operator&&this.lhs.isEquivalent(e.lhs)&&this.rhs.isEquivalent(e.rhs)}isConstant(){return!1}visitExpression(e,n){return e.visitBinaryOperatorExpr(this,n)}clone(){return new t(this.operator,this.lhs.clone(),this.rhs.clone(),this.type,this.sourceSpan)}},Ct=class t extends b{receiver;name;constructor(e,n,s,r){super(s,r),this.receiver=e,this.name=n}get index(){return this.name}isEquivalent(e){return e instanceof t&&this.receiver.isEquivalent(e.receiver)&&this.name===e.name}isConstant(){return!1}visitExpression(e,n){return e.visitReadPropExpr(this,n)}set(e){return new xt(this.receiver,this.name,e,null,this.sourceSpan)}clone(){return new t(this.receiver.clone(),this.name,this.type,this.sourceSpan)}},Tt=class t extends b{receiver;index;constructor(e,n,s,r){super(s,r),this.receiver=e,this.index=n}isEquivalent(e){return e instanceof t&&this.receiver.isEquivalent(e.receiver)&&this.index.isEquivalent(e.index)}isConstant(){return!1}visitExpression(e,n){return e.visitReadKeyExpr(this,n)}set(e){return new wt(this.receiver,this.index,e,null,this.sourceSpan)}clone(){return new t(this.receiver.clone(),this.index.clone(),this.type,this.sourceSpan)}},kt=class t extends b{entries;constructor(e,n,s){super(n,s),this.entries=e}isConstant(){return this.entries.every(e=>e.isConstant())}isEquivalent(e){return e instanceof t&&ct(this.entries,e.entries)}visitExpression(e,n){return e.visitLiteralArrayExpr(this,n)}clone(){return new t(this.entries.map(e=>e.clone()),this.type,this.sourceSpan)}};var bt=class t extends b{entries;valueType=null;constructor(e,n,s){super(n,s),this.entries=e,n&&(this.valueType=n.valueType)}isEquivalent(e){return e instanceof t&&ct(this.entries,e.entries)}isConstant(){return this.entries.every(e=>e.value.isConstant())}visitExpression(e,n){return e.visitLiteralMapExpr(this,n)}clone(){let e=this.entries.map(n=>n.clone());return new t(e,this.type,this.sourceSpan)}};var Ae;(function(t){t[t.None=0]="None",t[t.Final=1]="Final",t[t.Private=2]="Private",t[t.Exported=4]="Exported",t[t.Static=8]="Static"})(Ae||(Ae={}));var it=class{modifiers;sourceSpan;leadingComments;constructor(e=Ae.None,n=null,s){this.modifiers=e,this.sourceSpan=n,this.leadingComments=s}hasModifier(e){return(this.modifiers&e)!==0}addLeadingComment(e){this.leadingComments=this.leadingComments??[],this.leadingComments.push(e)}},It=class t extends it{name;value;type;constructor(e,n,s,r,i,a){super(r,i,a),this.name=e,this.value=n,this.type=s||n&&n.type||null}isEquivalent(e){return e instanceof t&&this.name===e.name&&(this.value?!!e.value&&this.value.isEquivalent(e.value):!e.value)}visitStatement(e,n){return e.visitDeclareVarStmt(this,n)}};var Nt=class t extends it{expr;constructor(e,n,s){super(Ae.None,n,s),this.expr=e}isEquivalent(e){return e instanceof t&&this.expr.isEquivalent(e.expr)}visitStatement(e,n){return e.visitExpressionStmt(this,n)}};function Ks(t,e,n){return new st(t,e,n)}var oi=Ks("<unknown>");var mn=class t{static INSTANCE=new t;keyOf(e){if(e instanceof rt&&typeof e.value=="string")return`"${e.value}"`;if(e instanceof rt)return String(e.value);if(e instanceof kt){let n=[];for(let s of e.entries)n.push(this.keyOf(s));return`[${n.join(",")}]`}else if(e instanceof bt){let n=[];for(let s of e.entries){let r=s.key;s.quoted&&(r=`"${r}"`),n.push(r+":"+this.keyOf(s.value))}return`{${n.join(",")}}`}else{if(e instanceof yt)return`import("${e.value.moduleName}", ${e.value.name})`;if(e instanceof st)return`read(${e.name})`;if(e instanceof gt)return`typeof(${this.keyOf(e.expr)})`;throw new Error(`${this.constructor.name} does not handle expressions of type ${e.constructor.name}`)}}};var o="@angular/core",p=class{static NEW_METHOD="factory";static TRANSFORM_METHOD="transform";static PATCH_DEPS="patchedDeps";static core={name:null,moduleName:o};static namespaceHTML={name:"\u0275\u0275namespaceHTML",moduleName:o};static namespaceMathML={name:"\u0275\u0275namespaceMathML",moduleName:o};static namespaceSVG={name:"\u0275\u0275namespaceSVG",moduleName:o};static element={name:"\u0275\u0275element",moduleName:o};static elementStart={name:"\u0275\u0275elementStart",moduleName:o};static elementEnd={name:"\u0275\u0275elementEnd",moduleName:o};static advance={name:"\u0275\u0275advance",moduleName:o};static syntheticHostProperty={name:"\u0275\u0275syntheticHostProperty",moduleName:o};static syntheticHostListener={name:"\u0275\u0275syntheticHostListener",moduleName:o};static attribute={name:"\u0275\u0275attribute",moduleName:o};static classProp={name:"\u0275\u0275classProp",moduleName:o};static elementContainerStart={name:"\u0275\u0275elementContainerStart",moduleName:o};static elementContainerEnd={name:"\u0275\u0275elementContainerEnd",moduleName:o};static elementContainer={name:"\u0275\u0275elementContainer",moduleName:o};static styleMap={name:"\u0275\u0275styleMap",moduleName:o};static classMap={name:"\u0275\u0275classMap",moduleName:o};static styleProp={name:"\u0275\u0275styleProp",moduleName:o};static interpolate={name:"\u0275\u0275interpolate",moduleName:o};static interpolate1={name:"\u0275\u0275interpolate1",moduleName:o};static interpolate2={name:"\u0275\u0275interpolate2",moduleName:o};static interpolate3={name:"\u0275\u0275interpolate3",moduleName:o};static interpolate4={name:"\u0275\u0275interpolate4",moduleName:o};static interpolate5={name:"\u0275\u0275interpolate5",moduleName:o};static interpolate6={name:"\u0275\u0275interpolate6",moduleName:o};static interpolate7={name:"\u0275\u0275interpolate7",moduleName:o};static interpolate8={name:"\u0275\u0275interpolate8",moduleName:o};static interpolateV={name:"\u0275\u0275interpolateV",moduleName:o};static nextContext={name:"\u0275\u0275nextContext",moduleName:o};static resetView={name:"\u0275\u0275resetView",moduleName:o};static templateCreate={name:"\u0275\u0275template",moduleName:o};static defer={name:"\u0275\u0275defer",moduleName:o};static deferWhen={name:"\u0275\u0275deferWhen",moduleName:o};static deferOnIdle={name:"\u0275\u0275deferOnIdle",moduleName:o};static deferOnImmediate={name:"\u0275\u0275deferOnImmediate",moduleName:o};static deferOnTimer={name:"\u0275\u0275deferOnTimer",moduleName:o};static deferOnHover={name:"\u0275\u0275deferOnHover",moduleName:o};static deferOnInteraction={name:"\u0275\u0275deferOnInteraction",moduleName:o};static deferOnViewport={name:"\u0275\u0275deferOnViewport",moduleName:o};static deferPrefetchWhen={name:"\u0275\u0275deferPrefetchWhen",moduleName:o};static deferPrefetchOnIdle={name:"\u0275\u0275deferPrefetchOnIdle",moduleName:o};static deferPrefetchOnImmediate={name:"\u0275\u0275deferPrefetchOnImmediate",moduleName:o};static deferPrefetchOnTimer={name:"\u0275\u0275deferPrefetchOnTimer",moduleName:o};static deferPrefetchOnHover={name:"\u0275\u0275deferPrefetchOnHover",moduleName:o};static deferPrefetchOnInteraction={name:"\u0275\u0275deferPrefetchOnInteraction",moduleName:o};static deferPrefetchOnViewport={name:"\u0275\u0275deferPrefetchOnViewport",moduleName:o};static deferHydrateWhen={name:"\u0275\u0275deferHydrateWhen",moduleName:o};static deferHydrateNever={name:"\u0275\u0275deferHydrateNever",moduleName:o};static deferHydrateOnIdle={name:"\u0275\u0275deferHydrateOnIdle",moduleName:o};static deferHydrateOnImmediate={name:"\u0275\u0275deferHydrateOnImmediate",moduleName:o};static deferHydrateOnTimer={name:"\u0275\u0275deferHydrateOnTimer",moduleName:o};static deferHydrateOnHover={name:"\u0275\u0275deferHydrateOnHover",moduleName:o};static deferHydrateOnInteraction={name:"\u0275\u0275deferHydrateOnInteraction",moduleName:o};static deferHydrateOnViewport={name:"\u0275\u0275deferHydrateOnViewport",moduleName:o};static deferEnableTimerScheduling={name:"\u0275\u0275deferEnableTimerScheduling",moduleName:o};static conditionalCreate={name:"\u0275\u0275conditionalCreate",moduleName:o};static conditionalBranchCreate={name:"\u0275\u0275conditionalBranchCreate",moduleName:o};static conditional={name:"\u0275\u0275conditional",moduleName:o};static repeater={name:"\u0275\u0275repeater",moduleName:o};static repeaterCreate={name:"\u0275\u0275repeaterCreate",moduleName:o};static repeaterTrackByIndex={name:"\u0275\u0275repeaterTrackByIndex",moduleName:o};static repeaterTrackByIdentity={name:"\u0275\u0275repeaterTrackByIdentity",moduleName:o};static componentInstance={name:"\u0275\u0275componentInstance",moduleName:o};static text={name:"\u0275\u0275text",moduleName:o};static enableBindings={name:"\u0275\u0275enableBindings",moduleName:o};static disableBindings={name:"\u0275\u0275disableBindings",moduleName:o};static getCurrentView={name:"\u0275\u0275getCurrentView",moduleName:o};static textInterpolate={name:"\u0275\u0275textInterpolate",moduleName:o};static textInterpolate1={name:"\u0275\u0275textInterpolate1",moduleName:o};static textInterpolate2={name:"\u0275\u0275textInterpolate2",moduleName:o};static textInterpolate3={name:"\u0275\u0275textInterpolate3",moduleName:o};static textInterpolate4={name:"\u0275\u0275textInterpolate4",moduleName:o};static textInterpolate5={name:"\u0275\u0275textInterpolate5",moduleName:o};static textInterpolate6={name:"\u0275\u0275textInterpolate6",moduleName:o};static textInterpolate7={name:"\u0275\u0275textInterpolate7",moduleName:o};static textInterpolate8={name:"\u0275\u0275textInterpolate8",moduleName:o};static textInterpolateV={name:"\u0275\u0275textInterpolateV",moduleName:o};static restoreView={name:"\u0275\u0275restoreView",moduleName:o};static pureFunction0={name:"\u0275\u0275pureFunction0",moduleName:o};static pureFunction1={name:"\u0275\u0275pureFunction1",moduleName:o};static pureFunction2={name:"\u0275\u0275pureFunction2",moduleName:o};static pureFunction3={name:"\u0275\u0275pureFunction3",moduleName:o};static pureFunction4={name:"\u0275\u0275pureFunction4",moduleName:o};static pureFunction5={name:"\u0275\u0275pureFunction5",moduleName:o};static pureFunction6={name:"\u0275\u0275pureFunction6",moduleName:o};static pureFunction7={name:"\u0275\u0275pureFunction7",moduleName:o};static pureFunction8={name:"\u0275\u0275pureFunction8",moduleName:o};static pureFunctionV={name:"\u0275\u0275pureFunctionV",moduleName:o};static pipeBind1={name:"\u0275\u0275pipeBind1",moduleName:o};static pipeBind2={name:"\u0275\u0275pipeBind2",moduleName:o};static pipeBind3={name:"\u0275\u0275pipeBind3",moduleName:o};static pipeBind4={name:"\u0275\u0275pipeBind4",moduleName:o};static pipeBindV={name:"\u0275\u0275pipeBindV",moduleName:o};static domProperty={name:"\u0275\u0275domProperty",moduleName:o};static property={name:"\u0275\u0275property",moduleName:o};static i18n={name:"\u0275\u0275i18n",moduleName:o};static i18nAttributes={name:"\u0275\u0275i18nAttributes",moduleName:o};static i18nExp={name:"\u0275\u0275i18nExp",moduleName:o};static i18nStart={name:"\u0275\u0275i18nStart",moduleName:o};static i18nEnd={name:"\u0275\u0275i18nEnd",moduleName:o};static i18nApply={name:"\u0275\u0275i18nApply",moduleName:o};static i18nPostprocess={name:"\u0275\u0275i18nPostprocess",moduleName:o};static pipe={name:"\u0275\u0275pipe",moduleName:o};static projection={name:"\u0275\u0275projection",moduleName:o};static projectionDef={name:"\u0275\u0275projectionDef",moduleName:o};static reference={name:"\u0275\u0275reference",moduleName:o};static inject={name:"\u0275\u0275inject",moduleName:o};static injectAttribute={name:"\u0275\u0275injectAttribute",moduleName:o};static directiveInject={name:"\u0275\u0275directiveInject",moduleName:o};static invalidFactory={name:"\u0275\u0275invalidFactory",moduleName:o};static invalidFactoryDep={name:"\u0275\u0275invalidFactoryDep",moduleName:o};static templateRefExtractor={name:"\u0275\u0275templateRefExtractor",moduleName:o};static forwardRef={name:"forwardRef",moduleName:o};static resolveForwardRef={name:"resolveForwardRef",moduleName:o};static replaceMetadata={name:"\u0275\u0275replaceMetadata",moduleName:o};static getReplaceMetadataURL={name:"\u0275\u0275getReplaceMetadataURL",moduleName:o};static \u0275\u0275defineInjectable={name:"\u0275\u0275defineInjectable",moduleName:o};static declareInjectable={name:"\u0275\u0275ngDeclareInjectable",moduleName:o};static InjectableDeclaration={name:"\u0275\u0275InjectableDeclaration",moduleName:o};static resolveWindow={name:"\u0275\u0275resolveWindow",moduleName:o};static resolveDocument={name:"\u0275\u0275resolveDocument",moduleName:o};static resolveBody={name:"\u0275\u0275resolveBody",moduleName:o};static getComponentDepsFactory={name:"\u0275\u0275getComponentDepsFactory",moduleName:o};static defineComponent={name:"\u0275\u0275defineComponent",moduleName:o};static declareComponent={name:"\u0275\u0275ngDeclareComponent",moduleName:o};static setComponentScope={name:"\u0275\u0275setComponentScope",moduleName:o};static ChangeDetectionStrategy={name:"ChangeDetectionStrategy",moduleName:o};static ViewEncapsulation={name:"ViewEncapsulation",moduleName:o};static ComponentDeclaration={name:"\u0275\u0275ComponentDeclaration",moduleName:o};static FactoryDeclaration={name:"\u0275\u0275FactoryDeclaration",moduleName:o};static declareFactory={name:"\u0275\u0275ngDeclareFactory",moduleName:o};static FactoryTarget={name:"\u0275\u0275FactoryTarget",moduleName:o};static defineDirective={name:"\u0275\u0275defineDirective",moduleName:o};static declareDirective={name:"\u0275\u0275ngDeclareDirective",moduleName:o};static DirectiveDeclaration={name:"\u0275\u0275DirectiveDeclaration",moduleName:o};static InjectorDef={name:"\u0275\u0275InjectorDef",moduleName:o};static InjectorDeclaration={name:"\u0275\u0275InjectorDeclaration",moduleName:o};static defineInjector={name:"\u0275\u0275defineInjector",moduleName:o};static declareInjector={name:"\u0275\u0275ngDeclareInjector",moduleName:o};static NgModuleDeclaration={name:"\u0275\u0275NgModuleDeclaration",moduleName:o};static ModuleWithProviders={name:"ModuleWithProviders",moduleName:o};static defineNgModule={name:"\u0275\u0275defineNgModule",moduleName:o};static declareNgModule={name:"\u0275\u0275ngDeclareNgModule",moduleName:o};static setNgModuleScope={name:"\u0275\u0275setNgModuleScope",moduleName:o};static registerNgModuleType={name:"\u0275\u0275registerNgModuleType",moduleName:o};static PipeDeclaration={name:"\u0275\u0275PipeDeclaration",moduleName:o};static definePipe={name:"\u0275\u0275definePipe",moduleName:o};static declarePipe={name:"\u0275\u0275ngDeclarePipe",moduleName:o};static declareClassMetadata={name:"\u0275\u0275ngDeclareClassMetadata",moduleName:o};static declareClassMetadataAsync={name:"\u0275\u0275ngDeclareClassMetadataAsync",moduleName:o};static setClassMetadata={name:"\u0275setClassMetadata",moduleName:o};static setClassMetadataAsync={name:"\u0275setClassMetadataAsync",moduleName:o};static setClassDebugInfo={name:"\u0275setClassDebugInfo",moduleName:o};static queryRefresh={name:"\u0275\u0275queryRefresh",moduleName:o};static viewQuery={name:"\u0275\u0275viewQuery",moduleName:o};static loadQuery={name:"\u0275\u0275loadQuery",moduleName:o};static contentQuery={name:"\u0275\u0275contentQuery",moduleName:o};static viewQuerySignal={name:"\u0275\u0275viewQuerySignal",moduleName:o};static contentQuerySignal={name:"\u0275\u0275contentQuerySignal",moduleName:o};static queryAdvance={name:"\u0275\u0275queryAdvance",moduleName:o};static twoWayProperty={name:"\u0275\u0275twoWayProperty",moduleName:o};static twoWayBindingSet={name:"\u0275\u0275twoWayBindingSet",moduleName:o};static twoWayListener={name:"\u0275\u0275twoWayListener",moduleName:o};static declareLet={name:"\u0275\u0275declareLet",moduleName:o};static storeLet={name:"\u0275\u0275storeLet",moduleName:o};static readContextLet={name:"\u0275\u0275readContextLet",moduleName:o};static attachSourceLocations={name:"\u0275\u0275attachSourceLocations",moduleName:o};static NgOnChangesFeature={name:"\u0275\u0275NgOnChangesFeature",moduleName:o};static InheritDefinitionFeature={name:"\u0275\u0275InheritDefinitionFeature",moduleName:o};static CopyDefinitionFeature={name:"\u0275\u0275CopyDefinitionFeature",moduleName:o};static ProvidersFeature={name:"\u0275\u0275ProvidersFeature",moduleName:o};static HostDirectivesFeature={name:"\u0275\u0275HostDirectivesFeature",moduleName:o};static ExternalStylesFeature={name:"\u0275\u0275ExternalStylesFeature",moduleName:o};static listener={name:"\u0275\u0275listener",moduleName:o};static getInheritedFactory={name:"\u0275\u0275getInheritedFactory",moduleName:o};static sanitizeHtml={name:"\u0275\u0275sanitizeHtml",moduleName:o};static sanitizeStyle={name:"\u0275\u0275sanitizeStyle",moduleName:o};static sanitizeResourceUrl={name:"\u0275\u0275sanitizeResourceUrl",moduleName:o};static sanitizeScript={name:"\u0275\u0275sanitizeScript",moduleName:o};static sanitizeUrl={name:"\u0275\u0275sanitizeUrl",moduleName:o};static sanitizeUrlOrResourceUrl={name:"\u0275\u0275sanitizeUrlOrResourceUrl",moduleName:o};static trustConstantHtml={name:"\u0275\u0275trustConstantHtml",moduleName:o};static trustConstantResourceUrl={name:"\u0275\u0275trustConstantResourceUrl",moduleName:o};static validateIframeAttribute={name:"\u0275\u0275validateIframeAttribute",moduleName:o};static InputSignalBrandWriteType={name:"\u0275INPUT_SIGNAL_BRAND_WRITE_TYPE",moduleName:o};static UnwrapDirectiveSignalInputs={name:"\u0275UnwrapDirectiveSignalInputs",moduleName:o};static unwrapWritableSignal={name:"\u0275unwrapWritableSignal",moduleName:o}};var At=class{full;major;minor;patch;constructor(e){this.full=e;let n=e.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}};var gn;(function(t){t[t.Class=0]="Class",t[t.Function=1]="Function"})(gn||(gn={}));var Pe=class{input;errLocation;ctxLocation;message;constructor(e,n,s,r){this.input=n,this.errLocation=s,this.ctxLocation=r,this.message=`Parser Error: ${e} ${s} [${n}] in ${r}`}},J=class{start;end;constructor(e,n){this.start=e,this.end=n}toAbsolute(e){return new O(e+this.start,e+this.end)}},S=class{span;sourceSpan;constructor(e,n){this.span=e,this.sourceSpan=n}toString(){return"AST"}},ae=class extends S{nameSpan;constructor(e,n,s){super(e,n),this.nameSpan=s}},P=class extends S{visit(e,n=null){}},Y=class extends S{visit(e,n=null){return e.visitImplicitReceiver(this,n)}},Pt=class extends Y{visit(e,n=null){var s;return(s=e.visitThisReceiver)==null?void 0:s.call(e,this,n)}},Le=class extends S{expressions;constructor(e,n,s){super(e,n),this.expressions=s}visit(e,n=null){return e.visitChain(this,n)}},Me=class extends S{condition;trueExp;falseExp;constructor(e,n,s,r,i){super(e,n),this.condition=s,this.trueExp=r,this.falseExp=i}visit(e,n=null){return e.visitConditional(this,n)}},le=class extends ae{receiver;name;constructor(e,n,s,r,i){super(e,n,s),this.receiver=r,this.name=i}visit(e,n=null){return e.visitPropertyRead(this,n)}},$e=class extends ae{receiver;name;value;constructor(e,n,s,r,i,a){super(e,n,s),this.receiver=r,this.name=i,this.value=a}visit(e,n=null){return e.visitPropertyWrite(this,n)}},ce=class extends ae{receiver;name;constructor(e,n,s,r,i){super(e,n,s),this.receiver=r,this.name=i}visit(e,n=null){return e.visitSafePropertyRead(this,n)}},Re=class extends S{receiver;key;constructor(e,n,s,r){super(e,n),this.receiver=s,this.key=r}visit(e,n=null){return e.visitKeyedRead(this,n)}},ue=class extends S{receiver;key;constructor(e,n,s,r){super(e,n),this.receiver=s,this.key=r}visit(e,n=null){return e.visitSafeKeyedRead(this,n)}},De=class extends S{receiver;key;value;constructor(e,n,s,r,i){super(e,n),this.receiver=s,this.key=r,this.value=i}visit(e,n=null){return e.visitKeyedWrite(this,n)}},Be=class extends ae{exp;name;args;constructor(e,n,s,r,i,a){super(e,n,a),this.exp=s,this.name=r,this.args=i}visit(e,n=null){return e.visitPipe(this,n)}},I=class extends S{value;constructor(e,n,s){super(e,n),this.value=s}visit(e,n=null){return e.visitLiteralPrimitive(this,n)}},Oe=class extends S{expressions;constructor(e,n,s){super(e,n),this.expressions=s}visit(e,n=null){return e.visitLiteralArray(this,n)}},Fe=class extends S{keys;values;constructor(e,n,s,r){super(e,n),this.keys=s,this.values=r}visit(e,n=null){return e.visitLiteralMap(this,n)}},Ut=class extends S{strings;expressions;constructor(e,n,s,r){super(e,n),this.strings=s,this.expressions=r}visit(e,n=null){return e.visitInterpolation(this,n)}},A=class extends S{operation;left;right;constructor(e,n,s,r,i){super(e,n),this.operation=s,this.left=r,this.right=i}visit(e,n=null){return e.visitBinary(this,n)}},X=class t extends A{operator;expr;left=null;right=null;operation=null;static createMinus(e,n,s){return new t(e,n,"-",s,"-",new I(e,n,0),s)}static createPlus(e,n,s){return new t(e,n,"+",s,"-",s,new I(e,n,0))}constructor(e,n,s,r,i,a,l){super(e,n,i,a,l),this.operator=s,this.expr=r}visit(e,n=null){return e.visitUnary!==void 0?e.visitUnary(this,n):e.visitBinary(this,n)}},Q=class extends S{expression;constructor(e,n,s){super(e,n),this.expression=s}visit(e,n=null){return e.visitPrefixNot(this,n)}},K=class extends S{expression;constructor(e,n,s){super(e,n),this.expression=s}visit(e,n=null){return e.visitTypeofExpression(this,n)}},Z=class extends S{expression;constructor(e,n,s){super(e,n),this.expression=s}visit(e,n=null){return e.visitVoidExpression(this,n)}},Ve=class extends S{expression;constructor(e,n,s){super(e,n),this.expression=s}visit(e,n=null){return e.visitNonNullAssert(this,n)}},Ue=class extends S{receiver;args;argumentSpan;constructor(e,n,s,r,i){super(e,n),this.receiver=s,this.args=r,this.argumentSpan=i}visit(e,n=null){return e.visitCall(this,n)}},pe=class extends S{receiver;args;argumentSpan;constructor(e,n,s,r,i){super(e,n),this.receiver=s,this.args=r,this.argumentSpan=i}visit(e,n=null){return e.visitSafeCall(this,n)}},he=class extends S{tag;template;constructor(e,n,s,r){super(e,n),this.tag=s,this.template=r}visit(e,n){return e.visitTaggedTemplateLiteral(this,n)}},fe=class extends S{elements;expressions;constructor(e,n,s,r){super(e,n),this.elements=s,this.expressions=r}visit(e,n){return e.visitTemplateLiteral(this,n)}},de=class extends S{text;constructor(e,n,s){super(e,n),this.text=s}visit(e,n){return e.visitTemplateLiteralElement(this,n)}},He=class extends S{expression;constructor(e,n,s){super(e,n),this.expression=s}visit(e,n){return e.visitParenthesizedExpression(this,n)}},O=class{start;end;constructor(e,n){this.start=e,this.end=n}},W=class extends S{ast;source;location;errors;constructor(e,n,s,r,i){super(new J(0,n===null?0:n.length),new O(r,n===null?r:r+n.length)),this.ast=e,this.source=n,this.location=s,this.errors=i}visit(e,n=null){return e.visitASTWithSource?e.visitASTWithSource(this,n):this.ast.visit(e,n)}toString(){return`${this.source} in ${this.location}`}},me=class{sourceSpan;key;value;constructor(e,n,s){this.sourceSpan=e,this.key=n,this.value=s}},We=class{sourceSpan;key;value;constructor(e,n,s){this.sourceSpan=e,this.key=n,this.value=s}},Lt=class{visit(e,n){e.visit(this,n)}visitUnary(e,n){this.visit(e.expr,n)}visitBinary(e,n){this.visit(e.left,n),this.visit(e.right,n)}visitChain(e,n){this.visitAll(e.expressions,n)}visitConditional(e,n){this.visit(e.condition,n),this.visit(e.trueExp,n),this.visit(e.falseExp,n)}visitPipe(e,n){this.visit(e.exp,n),this.visitAll(e.args,n)}visitImplicitReceiver(e,n){}visitThisReceiver(e,n){}visitInterpolation(e,n){this.visitAll(e.expressions,n)}visitKeyedRead(e,n){this.visit(e.receiver,n),this.visit(e.key,n)}visitKeyedWrite(e,n){this.visit(e.receiver,n),this.visit(e.key,n),this.visit(e.value,n)}visitLiteralArray(e,n){this.visitAll(e.expressions,n)}visitLiteralMap(e,n){this.visitAll(e.values,n)}visitLiteralPrimitive(e,n){}visitPrefixNot(e,n){this.visit(e.expression,n)}visitTypeofExpression(e,n){this.visit(e.expression,n)}visitVoidExpression(e,n){this.visit(e.expression,n)}visitNonNullAssert(e,n){this.visit(e.expression,n)}visitPropertyRead(e,n){this.visit(e.receiver,n)}visitPropertyWrite(e,n){this.visit(e.receiver,n),this.visit(e.value,n)}visitSafePropertyRead(e,n){this.visit(e.receiver,n)}visitSafeKeyedRead(e,n){this.visit(e.receiver,n),this.visit(e.key,n)}visitCall(e,n){this.visit(e.receiver,n),this.visitAll(e.args,n)}visitSafeCall(e,n){this.visit(e.receiver,n),this.visitAll(e.args,n)}visitTemplateLiteral(e,n){for(let s=0;s<e.elements.length;s++){this.visit(e.elements[s],n);let r=s<e.expressions.length?e.expressions[s]:null;r!==null&&this.visit(r,n)}}visitTemplateLiteralElement(e,n){}visitTaggedTemplateLiteral(e,n){this.visit(e.tag,n),this.visit(e.template,n)}visitParenthesizedExpression(e,n){this.visit(e.expression,n)}visitAll(e,n){for(let s of e)this.visit(s,n)}};var vn;(function(t){t[t.DEFAULT=0]="DEFAULT",t[t.LITERAL_ATTR=1]="LITERAL_ATTR",t[t.ANIMATION=2]="ANIMATION",t[t.TWO_WAY=3]="TWO_WAY"})(vn||(vn={}));var wn;(function(t){t[t.Regular=0]="Regular",t[t.Animation=1]="Animation",t[t.TwoWay=2]="TwoWay"})(wn||(wn={}));var U;(function(t){t[t.Property=0]="Property",t[t.Attribute=1]="Attribute",t[t.Class=2]="Class",t[t.Style=3]="Style",t[t.Animation=4]="Animation",t[t.TwoWay=5]="TwoWay"})(U||(U={}));var xn;(function(t){t[t.RAW_TEXT=0]="RAW_TEXT",t[t.ESCAPABLE_RAW_TEXT=1]="ESCAPABLE_RAW_TEXT",t[t.PARSABLE_DATA=2]="PARSABLE_DATA"})(xn||(xn={}));var Zs=[/@/,/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];function er(t,e){if(e!=null&&!(Array.isArray(e)&&e.length==2))throw new Error(`Expected '${t}' to be an array, [start, end].`);if(e!=null){let n=e[0],s=e[1];Zs.forEach(r=>{if(r.test(n)||r.test(s))throw new Error(`['${n}', '${s}'] contains unusable interpolation symbol.`)})}}var Mt=class t{start;end;static fromArray(e){return e?(er("interpolation",e),new t(e[0],e[1])):ne}constructor(e,n){this.start=e,this.end=n}},ne=new Mt("{{","}}");var Qe=0;var Kn=9,tr=10,nr=11,sr=12,rr=13,Zn=32,ir=33,es=34,or=35,Ht=36,ar=37,Sn=38,ts=39,tt=40,se=41,En=42,ns=43,Ce=44,ss=45,re=46,$t=47,ie=58,Te=59,lr=60,Ke=61,cr=62,yn=63,ur=48;var pr=57,rs=65,hr=69;var is=90,nt=91,_n=92,ke=93,fr=94,Wt=95,os=97;var dr=101,mr=102,gr=110,vr=114,wr=116,xr=117,Sr=118;var as=122,ot=123,Cn=124,be=125,ls=160;var Rt=96;function Er(t){return t>=Kn&&t<=Zn||t==ls}function z(t){return ur<=t&&t<=pr}function yr(t){return t>=os&&t<=as||t>=rs&&t<=is}function Tn(t){return t===ts||t===es||t===Rt}var kn;(function(t){t[t.WARNING=0]="WARNING",t[t.ERROR=1]="ERROR"})(kn||(kn={}));var bn;(function(t){t[t.Inline=0]="Inline",t[t.SideEffect=1]="SideEffect",t[t.Omit=2]="Omit"})(bn||(bn={}));var In;(function(t){t[t.Global=0]="Global",t[t.Local=1]="Local"})(In||(In={}));var Nn;(function(t){t[t.Directive=0]="Directive",t[t.Pipe=1]="Pipe",t[t.NgModule=2]="NgModule"})(Nn||(Nn={}));var _r="(:(where|is)\\()?";var cs="-shadowcsshost",Cr="-shadowcsscontext",us="(?:\\(((?:\\([^)(]*\\)|[^)(]*)+?)\\))",ci=new RegExp(cs+us+"?([^,{]*)","gim"),ps=Cr+us+"?([^{]*)",ui=new RegExp(`${_r}(${ps})`,"gim"),pi=new RegExp(ps,"im"),Tr=cs+"-no-combinator",hi=new RegExp(`${Tr}(?![^(]*\\))`,"g");var hs="%COMMENT%",fi=new RegExp(hs,"g");var di=new RegExp(`(\\s*(?:${hs}\\s*)*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))`,"g");var kr="%COMMA_IN_PLACEHOLDER%",br="%SEMI_IN_PLACEHOLDER%",Ir="%COLON_IN_PLACEHOLDER%",mi=new RegExp(kr,"g"),gi=new RegExp(br,"g"),vi=new RegExp(Ir,"g");var d;(function(t){t[t.ListEnd=0]="ListEnd",t[t.Statement=1]="Statement",t[t.Variable=2]="Variable",t[t.ElementStart=3]="ElementStart",t[t.Element=4]="Element",t[t.Template=5]="Template",t[t.ElementEnd=6]="ElementEnd",t[t.ContainerStart=7]="ContainerStart",t[t.Container=8]="Container",t[t.ContainerEnd=9]="ContainerEnd",t[t.DisableBindings=10]="DisableBindings",t[t.ConditionalCreate=11]="ConditionalCreate",t[t.ConditionalBranchCreate=12]="ConditionalBranchCreate",t[t.Conditional=13]="Conditional",t[t.EnableBindings=14]="EnableBindings",t[t.Text=15]="Text",t[t.Listener=16]="Listener",t[t.InterpolateText=17]="InterpolateText",t[t.Binding=18]="Binding",t[t.Property=19]="Property",t[t.StyleProp=20]="StyleProp",t[t.ClassProp=21]="ClassProp",t[t.StyleMap=22]="StyleMap",t[t.ClassMap=23]="ClassMap",t[t.Advance=24]="Advance",t[t.Pipe=25]="Pipe",t[t.Attribute=26]="Attribute",t[t.ExtractedAttribute=27]="ExtractedAttribute",t[t.Defer=28]="Defer",t[t.DeferOn=29]="DeferOn",t[t.DeferWhen=30]="DeferWhen",t[t.I18nMessage=31]="I18nMessage",t[t.DomProperty=32]="DomProperty",t[t.Namespace=33]="Namespace",t[t.ProjectionDef=34]="ProjectionDef",t[t.Projection=35]="Projection",t[t.RepeaterCreate=36]="RepeaterCreate",t[t.Repeater=37]="Repeater",t[t.TwoWayProperty=38]="TwoWayProperty",t[t.TwoWayListener=39]="TwoWayListener",t[t.DeclareLet=40]="DeclareLet",t[t.StoreLet=41]="StoreLet",t[t.I18nStart=42]="I18nStart",t[t.I18n=43]="I18n",t[t.I18nEnd=44]="I18nEnd",t[t.I18nExpression=45]="I18nExpression",t[t.I18nApply=46]="I18nApply",t[t.IcuStart=47]="IcuStart",t[t.IcuEnd=48]="IcuEnd",t[t.IcuPlaceholder=49]="IcuPlaceholder",t[t.I18nContext=50]="I18nContext",t[t.I18nAttributes=51]="I18nAttributes",t[t.SourceLocation=52]="SourceLocation"})(d||(d={}));var ge;(function(t){t[t.LexicalRead=0]="LexicalRead",t[t.Context=1]="Context",t[t.TrackContext=2]="TrackContext",t[t.ReadVariable=3]="ReadVariable",t[t.NextContext=4]="NextContext",t[t.Reference=5]="Reference",t[t.StoreLet=6]="StoreLet",t[t.ContextLetReference=7]="ContextLetReference",t[t.GetCurrentView=8]="GetCurrentView",t[t.RestoreView=9]="RestoreView",t[t.ResetView=10]="ResetView",t[t.PureFunctionExpr=11]="PureFunctionExpr",t[t.PureFunctionParameterExpr=12]="PureFunctionParameterExpr",t[t.PipeBinding=13]="PipeBinding",t[t.PipeBindingVariadic=14]="PipeBindingVariadic",t[t.SafePropertyRead=15]="SafePropertyRead",t[t.SafeKeyedRead=16]="SafeKeyedRead",t[t.SafeInvokeFunction=17]="SafeInvokeFunction",t[t.SafeTernaryExpr=18]="SafeTernaryExpr",t[t.EmptyExpr=19]="EmptyExpr",t[t.AssignTemporaryExpr=20]="AssignTemporaryExpr",t[t.ReadTemporaryExpr=21]="ReadTemporaryExpr",t[t.SlotLiteralExpr=22]="SlotLiteralExpr",t[t.ConditionalCase=23]="ConditionalCase",t[t.ConstCollected=24]="ConstCollected",t[t.TwoWayBindingSet=25]="TwoWayBindingSet"})(ge||(ge={}));var An;(function(t){t[t.None=0]="None",t[t.AlwaysInline=1]="AlwaysInline"})(An||(An={}));var Pn;(function(t){t[t.Context=0]="Context",t[t.Identifier=1]="Identifier",t[t.SavedView=2]="SavedView",t[t.Alias=3]="Alias"})(Pn||(Pn={}));var Ln;(function(t){t[t.Normal=0]="Normal",t[t.TemplateDefinitionBuilder=1]="TemplateDefinitionBuilder"})(Ln||(Ln={}));var H;(function(t){t[t.Attribute=0]="Attribute",t[t.ClassName=1]="ClassName",t[t.StyleProperty=2]="StyleProperty",t[t.Property=3]="Property",t[t.Template=4]="Template",t[t.I18n=5]="I18n",t[t.Animation=6]="Animation",t[t.TwoWayProperty=7]="TwoWayProperty"})(H||(H={}));var Mn;(function(t){t[t.Creation=0]="Creation",t[t.Postproccessing=1]="Postproccessing"})(Mn||(Mn={}));var $n;(function(t){t[t.I18nText=0]="I18nText",t[t.I18nAttribute=1]="I18nAttribute"})($n||($n={}));var Rn;(function(t){t[t.None=0]="None",t[t.ElementTag=1]="ElementTag",t[t.TemplateTag=2]="TemplateTag",t[t.OpenTag=4]="OpenTag",t[t.CloseTag=8]="CloseTag",t[t.ExpressionIndex=16]="ExpressionIndex"})(Rn||(Rn={}));var Dn;(function(t){t[t.HTML=0]="HTML",t[t.SVG=1]="SVG",t[t.Math=2]="Math"})(Dn||(Dn={}));var Bn;(function(t){t[t.Idle=0]="Idle",t[t.Immediate=1]="Immediate",t[t.Timer=2]="Timer",t[t.Hover=3]="Hover",t[t.Interaction=4]="Interaction",t[t.Viewport=5]="Viewport",t[t.Never=6]="Never"})(Bn||(Bn={}));var On;(function(t){t[t.RootI18n=0]="RootI18n",t[t.Icu=1]="Icu",t[t.Attr=2]="Attr"})(On||(On={}));var Fn;(function(t){t[t.NgTemplate=0]="NgTemplate",t[t.Structural=1]="Structural",t[t.Block=2]="Block"})(Fn||(Fn={}));var Nr=Symbol("ConsumesSlot"),fs=Symbol("DependsOnSlotContext"),je=Symbol("ConsumesVars"),qt=Symbol("UsesVarOffset"),wi={[Nr]:!0,numSlotsUsed:1},xi={[fs]:!0},Si={[je]:!0};var at=class{strings;expressions;i18nPlaceholders;constructor(e,n,s){if(this.strings=e,this.expressions=n,this.i18nPlaceholders=s,s.length!==0&&s.length!==n.length)throw new Error(`Expected ${n.length} placeholders to match interpolation expression count, but got ${s.length}`)}};var ve=class extends b{constructor(e=null){super(null,e)}};var Vn=class t extends ve{target;value;sourceSpan;kind=ge.StoreLet;[je]=!0;[fs]=!0;constructor(e,n,s){super(),this.target=e,this.value=n,this.sourceSpan=s}visitExpression(){}isEquivalent(e){return e instanceof t&&e.target===this.target&&e.value.isEquivalent(this.value)}isConstant(){return!1}transformInternalExpressions(e,n){this.value=(this.value,void 0)}clone(){return new t(this.target,this.value,this.sourceSpan)}};var Un=class t extends ve{kind=ge.PureFunctionExpr;[je]=!0;[qt]=!0;varOffset=null;body;args;fn=null;constructor(e,n){super(),this.body=e,this.args=n}visitExpression(e,n){var s;(s=this.body)==null||s.visitExpression(e,n);for(let r of this.args)r.visitExpression(e,n)}isEquivalent(e){return!(e instanceof t)||e.args.length!==this.args.length?!1:e.body!==null&&this.body!==null&&e.body.isEquivalent(this.body)&&e.args.every((n,s)=>n.isEquivalent(this.args[s]))}isConstant(){return!1}transformInternalExpressions(e,n){this.body!==null?this.body=(this.body,n|Dt.InChildOperation,void 0):this.fn!==null&&(this.fn=(this.fn,void 0));for(let s=0;s<this.args.length;s++)this.args[s]=(this.args[s],void 0)}clone(){var n,s;let e=new t(((n=this.body)==null?void 0:n.clone())??null,this.args.map(r=>r.clone()));return e.fn=((s=this.fn)==null?void 0:s.clone())??null,e.varOffset=this.varOffset,e}};var Hn=class t extends ve{target;targetSlot;name;args;kind=ge.PipeBinding;[je]=!0;[qt]=!0;varOffset=null;constructor(e,n,s,r){super(),this.target=e,this.targetSlot=n,this.name=s,this.args=r}visitExpression(e,n){for(let s of this.args)s.visitExpression(e,n)}isEquivalent(){return!1}isConstant(){return!1}transformInternalExpressions(e,n){for(let s=0;s<this.args.length;s++)this.args[s]=(this.args[s],void 0)}clone(){let e=new t(this.target,this.targetSlot,this.name,this.args.map(n=>n.clone()));return e.varOffset=this.varOffset,e}},Wn=class t extends ve{target;targetSlot;name;args;numArgs;kind=ge.PipeBindingVariadic;[je]=!0;[qt]=!0;varOffset=null;constructor(e,n,s,r,i){super(),this.target=e,this.targetSlot=n,this.name=s,this.args=r,this.numArgs=i}visitExpression(e,n){this.args.visitExpression(e,n)}isEquivalent(){return!1}isConstant(){return!1}transformInternalExpressions(e,n){this.args=(this.args,void 0)}clone(){let e=new t(this.target,this.targetSlot,this.name,this.args.clone(),this.numArgs);return e.varOffset=this.varOffset,e}};var Dt;(function(t){t[t.None=0]="None",t[t.InChildOperation=1]="InChildOperation"})(Dt||(Dt={}));var Ei=new Set([d.Element,d.ElementStart,d.Container,d.ContainerStart,d.Template,d.RepeaterCreate,d.ConditionalCreate,d.ConditionalBranchCreate]);var qn;(function(t){t[t.Tmpl=0]="Tmpl",t[t.Host=1]="Host",t[t.Both=2]="Both"})(qn||(qn={}));var yi=new Map([[p.attribute,p.attribute],[p.classProp,p.classProp],[p.element,p.element],[p.elementContainer,p.elementContainer],[p.elementContainerEnd,p.elementContainerEnd],[p.elementContainerStart,p.elementContainerStart],[p.elementEnd,p.elementEnd],[p.elementStart,p.elementStart],[p.domProperty,p.domProperty],[p.i18nExp,p.i18nExp],[p.listener,p.listener],[p.listener,p.listener],[p.property,p.property],[p.styleProp,p.styleProp],[p.syntheticHostListener,p.syntheticHostListener],[p.syntheticHostProperty,p.syntheticHostProperty],[p.templateCreate,p.templateCreate],[p.twoWayProperty,p.twoWayProperty],[p.twoWayListener,p.twoWayListener],[p.declareLet,p.declareLet],[p.conditionalCreate,p.conditionalBranchCreate],[p.conditionalBranchCreate,p.conditionalBranchCreate]]);var _i=Object.freeze([]);var Ci=new Map([[d.ElementEnd,[d.ElementStart,d.Element]],[d.ContainerEnd,[d.ContainerStart,d.Container]],[d.I18nEnd,[d.I18nStart,d.I18n]]]),Ti=new Set([d.Pipe]);var Ar={},Pr="\uE500";Ar.ngsp=Pr;var jn;(function(t){t.HEX="hexadecimal",t.DEC="decimal"})(jn||(jn={}));var ds=` \f
\r	\v\u1680\u180E\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF`,ki=new RegExp(`[^${ds}]`),bi=new RegExp(`[${ds}]{2,}`,"g");var m;(function(t){t[t.Character=0]="Character",t[t.Identifier=1]="Identifier",t[t.PrivateIdentifier=2]="PrivateIdentifier",t[t.Keyword=3]="Keyword",t[t.String=4]="String",t[t.Operator=5]="Operator",t[t.Number=6]="Number",t[t.Error=7]="Error"})(m||(m={}));var q;(function(t){t[t.Plain=0]="Plain",t[t.TemplateLiteralPart=1]="TemplateLiteralPart",t[t.TemplateLiteralEnd=2]="TemplateLiteralEnd"})(q||(q={}));var Lr=["var","let","as","null","undefined","true","false","if","else","this","typeof","void","in"],qe=class{tokenize(e){return new Bt(e).scan()}},M=class{index;end;type;numValue;strValue;constructor(e,n,s,r,i){this.index=e,this.end=n,this.type=s,this.numValue=r,this.strValue=i}isCharacter(e){return this.type===m.Character&&this.numValue===e}isNumber(){return this.type===m.Number}isString(){return this.type===m.String}isOperator(e){return this.type===m.Operator&&this.strValue===e}isIdentifier(){return this.type===m.Identifier}isPrivateIdentifier(){return this.type===m.PrivateIdentifier}isKeyword(){return this.type===m.Keyword}isKeywordLet(){return this.type===m.Keyword&&this.strValue==="let"}isKeywordAs(){return this.type===m.Keyword&&this.strValue==="as"}isKeywordNull(){return this.type===m.Keyword&&this.strValue==="null"}isKeywordUndefined(){return this.type===m.Keyword&&this.strValue==="undefined"}isKeywordTrue(){return this.type===m.Keyword&&this.strValue==="true"}isKeywordFalse(){return this.type===m.Keyword&&this.strValue==="false"}isKeywordThis(){return this.type===m.Keyword&&this.strValue==="this"}isKeywordTypeof(){return this.type===m.Keyword&&this.strValue==="typeof"}isKeywordVoid(){return this.type===m.Keyword&&this.strValue==="void"}isKeywordIn(){return this.type===m.Keyword&&this.strValue==="in"}isError(){return this.type===m.Error}toNumber(){return this.type===m.Number?this.numValue:-1}isTemplateLiteralPart(){return this.isString()&&this.kind===q.TemplateLiteralPart}isTemplateLiteralEnd(){return this.isString()&&this.kind===q.TemplateLiteralEnd}isTemplateLiteralInterpolationStart(){return this.isOperator("${")}isTemplateLiteralInterpolationEnd(){return this.isOperator("}")}toString(){switch(this.type){case m.Character:case m.Identifier:case m.Keyword:case m.Operator:case m.PrivateIdentifier:case m.String:case m.Error:return this.strValue;case m.Number:return this.numValue.toString();default:return null}}},Ie=class extends M{kind;constructor(e,n,s,r){super(e,n,m.String,0,s),this.kind=r}};function Ze(t,e,n){return new M(t,e,m.Character,n,String.fromCharCode(n))}function Mr(t,e,n){return new M(t,e,m.Identifier,0,n)}function $r(t,e,n){return new M(t,e,m.PrivateIdentifier,0,n)}function Rr(t,e,n){return new M(t,e,m.Keyword,0,n)}function _e(t,e,n){return new M(t,e,m.Operator,0,n)}function Dr(t,e,n){return new M(t,e,m.Number,n,"")}function Br(t,e,n){return new M(t,e,m.Error,0,n)}var et=new M(-1,-1,m.Character,0,""),Bt=class{input;tokens=[];length;peek=0;index=-1;braceStack=[];constructor(e){this.input=e,this.length=e.length,this.advance()}scan(){let e=this.scanToken();for(;e!==null;)this.tokens.push(e),e=this.scanToken();return this.tokens}advance(){this.peek=++this.index>=this.length?Qe:this.input.charCodeAt(this.index)}scanToken(){let e=this.input,n=this.length,s=this.peek,r=this.index;for(;s<=Zn;)if(++r>=n){s=Qe;break}else s=e.charCodeAt(r);if(this.peek=s,this.index=r,r>=n)return null;if(zn(s))return this.scanIdentifier();if(z(s))return this.scanNumber(r);let i=r;switch(s){case re:return this.advance(),z(this.peek)?this.scanNumber(i):Ze(i,this.index,re);case tt:case se:case nt:case ke:case Ce:case ie:case Te:return this.scanCharacter(i,s);case ot:return this.scanOpenBrace(i,s);case be:return this.scanCloseBrace(i,s);case ts:case es:return this.scanString();case Rt:return this.advance(),this.scanTemplateLiteralPart(i);case or:return this.scanPrivateIdentifier();case ns:case ss:case $t:case ar:case fr:return this.scanOperator(i,String.fromCharCode(s));case En:return this.scanComplexOperator(i,"*",En,"*");case yn:return this.scanQuestion(i);case lr:case cr:return this.scanComplexOperator(i,String.fromCharCode(s),Ke,"=");case ir:case Ke:return this.scanComplexOperator(i,String.fromCharCode(s),Ke,"=",Ke,"=");case Sn:return this.scanComplexOperator(i,"&",Sn,"&");case Cn:return this.scanComplexOperator(i,"|",Cn,"|");case ls:for(;Er(this.peek);)this.advance();return this.scanToken()}return this.advance(),this.error(`Unexpected character [${String.fromCharCode(s)}]`,0)}scanCharacter(e,n){return this.advance(),Ze(e,this.index,n)}scanOperator(e,n){return this.advance(),_e(e,this.index,n)}scanOpenBrace(e,n){return this.braceStack.push("expression"),this.advance(),Ze(e,this.index,n)}scanCloseBrace(e,n){return this.advance(),this.braceStack.pop()==="interpolation"?(this.tokens.push(_e(e,this.index,"}")),this.scanTemplateLiteralPart(this.index)):Ze(e,this.index,n)}scanComplexOperator(e,n,s,r,i,a){this.advance();let l=n;return this.peek==s&&(this.advance(),l+=r),i!=null&&this.peek==i&&(this.advance(),l+=a),_e(e,this.index,l)}scanIdentifier(){let e=this.index;for(this.advance();Gn(this.peek);)this.advance();let n=this.input.substring(e,this.index);return Lr.indexOf(n)>-1?Rr(e,this.index,n):Mr(e,this.index,n)}scanPrivateIdentifier(){let e=this.index;if(this.advance(),!zn(this.peek))return this.error("Invalid character [#]",-1);for(;Gn(this.peek);)this.advance();let n=this.input.substring(e,this.index);return $r(e,this.index,n)}scanNumber(e){let n=this.index===e,s=!1;for(this.advance();;){if(!z(this.peek))if(this.peek===Wt){if(!z(this.input.charCodeAt(this.index-1))||!z(this.input.charCodeAt(this.index+1)))return this.error("Invalid numeric separator",0);s=!0}else if(this.peek===re)n=!1;else if(Or(this.peek)){if(this.advance(),Fr(this.peek)&&this.advance(),!z(this.peek))return this.error("Invalid exponent",-1);n=!1}else break;this.advance()}let r=this.input.substring(e,this.index);s&&(r=r.replace(/_/g,""));let i=n?Ur(r):parseFloat(r);return Dr(e,this.index,i)}scanString(){let e=this.index,n=this.peek;this.advance();let s="",r=this.index,i=this.input;for(;this.peek!=n;)if(this.peek==_n){let l=this.scanStringBackslash(s,r);if(typeof l!="string")return l;s=l,r=this.index}else{if(this.peek==Qe)return this.error("Unterminated quote",0);this.advance()}let a=i.substring(r,this.index);return this.advance(),new Ie(e,this.index,s+a,q.Plain)}scanQuestion(e){this.advance();let n="?";return(this.peek===yn||this.peek===re)&&(n+=this.peek===re?".":"?",this.advance()),_e(e,this.index,n)}scanTemplateLiteralPart(e){let n="",s=this.index;for(;this.peek!==Rt;)if(this.peek===_n){let i=this.scanStringBackslash(n,s);if(typeof i!="string")return i;n=i,s=this.index}else if(this.peek===Ht){let i=this.index;if(this.advance(),this.peek===ot)return this.braceStack.push("interpolation"),this.tokens.push(new Ie(e,i,n+this.input.substring(s,i),q.TemplateLiteralPart)),this.advance(),_e(i,this.index,this.input.substring(i,this.index))}else{if(this.peek===Qe)return this.error("Unterminated template literal",0);this.advance()}let r=this.input.substring(s,this.index);return this.advance(),new Ie(e,this.index,n+r,q.TemplateLiteralEnd)}error(e,n){let s=this.index+n;return Br(s,this.index,`Lexer Error: ${e} at column ${s} in expression [${this.input}]`)}scanStringBackslash(e,n){e+=this.input.substring(n,this.index);let s;if(this.advance(),this.peek===xr){let r=this.input.substring(this.index+1,this.index+5);if(/^[0-9a-f]+$/i.test(r))s=parseInt(r,16);else return this.error(`Invalid unicode escape [\\u${r}]`,0);for(let i=0;i<5;i++)this.advance()}else s=Vr(this.peek),this.advance();return e+=String.fromCharCode(s),e}};function zn(t){return os<=t&&t<=as||rs<=t&&t<=is||t==Wt||t==Ht}function Gn(t){return yr(t)||z(t)||t==Wt||t==Ht}function Or(t){return t==dr||t==hr}function Fr(t){return t==ss||t==ns}function Vr(t){switch(t){case gr:return tr;case mr:return sr;case vr:return rr;case wr:return Kn;case Sr:return nr;default:return t}}function Ur(t){let e=parseInt(t);if(isNaN(e))throw new Error("Invalid integer literal when parsing "+t);return e}var Ot=class{strings;expressions;offsets;constructor(e,n,s){this.strings=e,this.expressions=n,this.offsets=s}},Ft=class{templateBindings;warnings;errors;constructor(e,n,s){this.templateBindings=e,this.warnings=n,this.errors=s}},we=class{_lexer;errors=[];constructor(e){this._lexer=e}parseAction(e,n,s,r=ne){this._checkNoInterpolation(e,n,r);let i=this._stripComments(e),a=this._lexer.tokenize(i),l=new G(e,n,s,a,1,this.errors,0).parseChain();return new W(l,e,n,s,this.errors)}parseBinding(e,n,s,r=ne){let i=this._parseBindingAst(e,n,s,r);return new W(i,e,n,s,this.errors)}checkSimpleExpression(e){let n=new Vt;return e.visit(n),n.errors}parseSimpleBinding(e,n,s,r=ne){let i=this._parseBindingAst(e,n,s,r),a=this.checkSimpleExpression(i);return a.length>0&&this._reportError(`Host binding expression cannot contain ${a.join(" ")}`,e,n),new W(i,e,n,s,this.errors)}_reportError(e,n,s,r){this.errors.push(new Pe(e,n,s,r))}_parseBindingAst(e,n,s,r){this._checkNoInterpolation(e,n,r);let i=this._stripComments(e),a=this._lexer.tokenize(i);return new G(e,n,s,a,0,this.errors,0).parseChain()}parseTemplateBindings(e,n,s,r,i){let a=this._lexer.tokenize(n);return new G(n,s,i,a,0,this.errors,0).parseTemplateBindings({source:e,span:new O(r,r+e.length)})}parseInterpolation(e,n,s,r,i=ne){let{strings:a,expressions:l,offsets:h}=this.splitInterpolation(e,n,r,i);if(l.length===0)return null;let f=[];for(let v=0;v<l.length;++v){let E=l[v].text,y=this._stripComments(E),T=this._lexer.tokenize(y),k=new G(e,n,s,T,0,this.errors,h[v]).parseChain();f.push(k)}return this.createInterpolationAst(a.map(v=>v.text),f,e,n,s)}parseInterpolationExpression(e,n,s){let r=this._stripComments(e),i=this._lexer.tokenize(r),a=new G(e,n,s,i,0,this.errors,0).parseChain(),l=["",""];return this.createInterpolationAst(l,[a],e,n,s)}createInterpolationAst(e,n,s,r,i){let a=new J(0,s.length),l=new Ut(a,a.toAbsolute(i),e,n);return new W(l,s,r,i,this.errors)}splitInterpolation(e,n,s,r=ne){let i=[],a=[],l=[],h=s?Hr(s):null,f=0,v=!1,E=!1,{start:y,end:T}=r;for(;f<e.length;)if(v){let k=f,F=k+y.length,Ee=this._getInterpolationEndIndex(e,T,F);if(Ee===-1){v=!1,E=!0;break}let ye=Ee+T.length,j=e.substring(F,Ee);j.trim().length===0&&this._reportError("Blank expressions are not allowed in interpolated strings",e,`at column ${f} in`,n),a.push({text:j,start:k,end:ye});let Ws=((h==null?void 0:h.get(k))??k)+y.length;l.push(Ws),f=ye,v=!1}else{let k=f;f=e.indexOf(y,f),f===-1&&(f=e.length);let F=e.substring(k,f);i.push({text:F,start:k,end:f}),v=!0}if(!v)if(E){let k=i[i.length-1];k.text+=e.substring(f),k.end=e.length}else i.push({text:e.substring(f),start:f,end:e.length});return new Ot(i,a,l)}wrapLiteralPrimitive(e,n,s){let r=new J(0,e==null?0:e.length);return new W(new I(r,r.toAbsolute(s),e),e,n,s,this.errors)}_stripComments(e){let n=this._commentStart(e);return n!=null?e.substring(0,n):e}_commentStart(e){let n=null;for(let s=0;s<e.length-1;s++){let r=e.charCodeAt(s),i=e.charCodeAt(s+1);if(r===$t&&i==$t&&n==null)return s;n===r?n=null:n==null&&Tn(r)&&(n=r)}return null}_checkNoInterpolation(e,n,{start:s,end:r}){let i=-1,a=-1;for(let l of this._forEachUnquotedChar(e,0))if(i===-1)e.startsWith(s)&&(i=l);else if(a=this._getInterpolationEndIndex(e,r,l),a>-1)break;i>-1&&a>-1&&this._reportError(`Got interpolation (${s}${r}) where expression was expected`,e,`at column ${i} in`,n)}_getInterpolationEndIndex(e,n,s){for(let r of this._forEachUnquotedChar(e,s)){if(e.startsWith(n,r))return r;if(e.startsWith("//",r))return e.indexOf(n,r)}return-1}*_forEachUnquotedChar(e,n){let s=null,r=0;for(let i=n;i<e.length;i++){let a=e[i];Tn(e.charCodeAt(i))&&(s===null||s===a)&&r%2===0?s=s===null?a:null:s===null&&(yield i),r=a==="\\"?r+1:0}}},oe;(function(t){t[t.None=0]="None",t[t.Writable=1]="Writable"})(oe||(oe={}));var G=class{input;location;absoluteOffset;tokens;parseFlags;errors;offset;rparensExpected=0;rbracketsExpected=0;rbracesExpected=0;context=oe.None;sourceSpanCache=new Map;index=0;constructor(e,n,s,r,i,a,l){this.input=e,this.location=n,this.absoluteOffset=s,this.tokens=r,this.parseFlags=i,this.errors=a,this.offset=l}peek(e){let n=this.index+e;return n<this.tokens.length?this.tokens[n]:et}get next(){return this.peek(0)}get atEOF(){return this.index>=this.tokens.length}get inputIndex(){return this.atEOF?this.currentEndIndex:this.next.index+this.offset}get currentEndIndex(){return this.index>0?this.peek(-1).end+this.offset:this.tokens.length===0?this.input.length+this.offset:this.next.index+this.offset}get currentAbsoluteOffset(){return this.absoluteOffset+this.inputIndex}span(e,n){let s=this.currentEndIndex;if(n!==void 0&&n>this.currentEndIndex&&(s=n),e>s){let r=s;s=e,e=r}return new J(e,s)}sourceSpan(e,n){let s=`${e}@${this.inputIndex}:${n}`;return this.sourceSpanCache.has(s)||this.sourceSpanCache.set(s,this.span(e,n).toAbsolute(this.absoluteOffset)),this.sourceSpanCache.get(s)}advance(){this.index++}withContext(e,n){this.context|=e;let s=n();return this.context^=e,s}consumeOptionalCharacter(e){return this.next.isCharacter(e)?(this.advance(),!0):!1}peekKeywordLet(){return this.next.isKeywordLet()}peekKeywordAs(){return this.next.isKeywordAs()}expectCharacter(e){this.consumeOptionalCharacter(e)||this.error(`Missing expected ${String.fromCharCode(e)}`)}consumeOptionalOperator(e){return this.next.isOperator(e)?(this.advance(),!0):!1}expectOperator(e){this.consumeOptionalOperator(e)||this.error(`Missing expected operator ${e}`)}prettyPrintToken(e){return e===et?"end of input":`token ${e}`}expectIdentifierOrKeyword(){let e=this.next;return!e.isIdentifier()&&!e.isKeyword()?(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier or keyword"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier or keyword`),null):(this.advance(),e.toString())}expectIdentifierOrKeywordOrString(){let e=this.next;return!e.isIdentifier()&&!e.isKeyword()&&!e.isString()?(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier, keyword or string"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier, keyword, or string`),""):(this.advance(),e.toString())}parseChain(){let e=[],n=this.inputIndex;for(;this.index<this.tokens.length;){let s=this.parsePipe();if(e.push(s),this.consumeOptionalCharacter(Te))for(this.parseFlags&1||this.error("Binding expression cannot contain chained expression");this.consumeOptionalCharacter(Te););else if(this.index<this.tokens.length){let r=this.index;if(this.error(`Unexpected token '${this.next}'`),this.index===r)break}}if(e.length===0){let s=this.offset,r=this.offset+this.input.length;return new P(this.span(s,r),this.sourceSpan(s,r))}return e.length==1?e[0]:new Le(this.span(n),this.sourceSpan(n),e)}parsePipe(){let e=this.inputIndex,n=this.parseExpression();if(this.consumeOptionalOperator("|")){this.parseFlags&1&&this.error("Cannot have a pipe in an action expression");do{let s=this.inputIndex,r=this.expectIdentifierOrKeyword(),i,a;r!==null?i=this.sourceSpan(s):(r="",a=this.next.index!==-1?this.next.index:this.input.length+this.offset,i=new J(a,a).toAbsolute(this.absoluteOffset));let l=[];for(;this.consumeOptionalCharacter(ie);)l.push(this.parseExpression());n=new Be(this.span(e),this.sourceSpan(e,a),n,r,l,i)}while(this.consumeOptionalOperator("|"))}return n}parseExpression(){return this.parseConditional()}parseConditional(){let e=this.inputIndex,n=this.parseLogicalOr();if(this.consumeOptionalOperator("?")){let s=this.parsePipe(),r;if(this.consumeOptionalCharacter(ie))r=this.parsePipe();else{let i=this.inputIndex,a=this.input.substring(e,i);this.error(`Conditional expression ${a} requires all 3 expressions`),r=new P(this.span(e),this.sourceSpan(e))}return new Me(this.span(e),this.sourceSpan(e),n,s,r)}else return n}parseLogicalOr(){let e=this.inputIndex,n=this.parseLogicalAnd();for(;this.consumeOptionalOperator("||");){let s=this.parseLogicalAnd();n=new A(this.span(e),this.sourceSpan(e),"||",n,s)}return n}parseLogicalAnd(){let e=this.inputIndex,n=this.parseNullishCoalescing();for(;this.consumeOptionalOperator("&&");){let s=this.parseNullishCoalescing();n=new A(this.span(e),this.sourceSpan(e),"&&",n,s)}return n}parseNullishCoalescing(){let e=this.inputIndex,n=this.parseEquality();for(;this.consumeOptionalOperator("??");){let s=this.parseEquality();n=new A(this.span(e),this.sourceSpan(e),"??",n,s)}return n}parseEquality(){let e=this.inputIndex,n=this.parseRelational();for(;this.next.type==m.Operator;){let s=this.next.strValue;switch(s){case"==":case"===":case"!=":case"!==":this.advance();let r=this.parseRelational();n=new A(this.span(e),this.sourceSpan(e),s,n,r);continue}break}return n}parseRelational(){let e=this.inputIndex,n=this.parseAdditive();for(;this.next.type==m.Operator||this.next.isKeywordIn;){let s=this.next.strValue;switch(s){case"<":case">":case"<=":case">=":case"in":this.advance();let r=this.parseAdditive();n=new A(this.span(e),this.sourceSpan(e),s,n,r);continue}break}return n}parseAdditive(){let e=this.inputIndex,n=this.parseMultiplicative();for(;this.next.type==m.Operator;){let s=this.next.strValue;switch(s){case"+":case"-":this.advance();let r=this.parseMultiplicative();n=new A(this.span(e),this.sourceSpan(e),s,n,r);continue}break}return n}parseMultiplicative(){let e=this.inputIndex,n=this.parseExponentiation();for(;this.next.type==m.Operator;){let s=this.next.strValue;switch(s){case"*":case"%":case"/":this.advance();let r=this.parseExponentiation();n=new A(this.span(e),this.sourceSpan(e),s,n,r);continue}break}return n}parseExponentiation(){let e=this.inputIndex,n=this.parsePrefix();for(;this.next.type==m.Operator&&this.next.strValue==="**";){(n instanceof X||n instanceof Q||n instanceof K||n instanceof Z)&&this.error("Unary operator used immediately before exponentiation expression. Parenthesis must be used to disambiguate operator precedence"),this.advance();let s=this.parseExponentiation();n=new A(this.span(e),this.sourceSpan(e),"**",n,s)}return n}parsePrefix(){if(this.next.type==m.Operator){let e=this.inputIndex,n=this.next.strValue,s;switch(n){case"+":return this.advance(),s=this.parsePrefix(),X.createPlus(this.span(e),this.sourceSpan(e),s);case"-":return this.advance(),s=this.parsePrefix(),X.createMinus(this.span(e),this.sourceSpan(e),s);case"!":return this.advance(),s=this.parsePrefix(),new Q(this.span(e),this.sourceSpan(e),s)}}else if(this.next.isKeywordTypeof()){this.advance();let e=this.inputIndex,n=this.parsePrefix();return new K(this.span(e),this.sourceSpan(e),n)}else if(this.next.isKeywordVoid()){this.advance();let e=this.inputIndex,n=this.parsePrefix();return new Z(this.span(e),this.sourceSpan(e),n)}return this.parseCallChain()}parseCallChain(){let e=this.inputIndex,n=this.parsePrimary();for(;;)if(this.consumeOptionalCharacter(re))n=this.parseAccessMember(n,e,!1);else if(this.consumeOptionalOperator("?."))this.consumeOptionalCharacter(tt)?n=this.parseCall(n,e,!0):n=this.consumeOptionalCharacter(nt)?this.parseKeyedReadOrWrite(n,e,!0):this.parseAccessMember(n,e,!0);else if(this.consumeOptionalCharacter(nt))n=this.parseKeyedReadOrWrite(n,e,!1);else if(this.consumeOptionalCharacter(tt))n=this.parseCall(n,e,!1);else if(this.consumeOptionalOperator("!"))n=new Ve(this.span(e),this.sourceSpan(e),n);else if(this.next.isTemplateLiteralEnd())n=this.parseNoInterpolationTaggedTemplateLiteral(n,e);else if(this.next.isTemplateLiteralPart())n=this.parseTaggedTemplateLiteral(n,e);else return n}parsePrimary(){let e=this.inputIndex;if(this.consumeOptionalCharacter(tt)){this.rparensExpected++;let n=this.parsePipe();return this.consumeOptionalCharacter(se)||(this.error("Missing closing parentheses"),this.consumeOptionalCharacter(se)),this.rparensExpected--,new He(this.span(e),this.sourceSpan(e),n)}else{if(this.next.isKeywordNull())return this.advance(),new I(this.span(e),this.sourceSpan(e),null);if(this.next.isKeywordUndefined())return this.advance(),new I(this.span(e),this.sourceSpan(e),void 0);if(this.next.isKeywordTrue())return this.advance(),new I(this.span(e),this.sourceSpan(e),!0);if(this.next.isKeywordFalse())return this.advance(),new I(this.span(e),this.sourceSpan(e),!1);if(this.next.isKeywordIn())return this.advance(),new I(this.span(e),this.sourceSpan(e),"in");if(this.next.isKeywordThis())return this.advance(),new Pt(this.span(e),this.sourceSpan(e));if(this.consumeOptionalCharacter(nt)){this.rbracketsExpected++;let n=this.parseExpressionList(ke);return this.rbracketsExpected--,this.expectCharacter(ke),new Oe(this.span(e),this.sourceSpan(e),n)}else{if(this.next.isCharacter(ot))return this.parseLiteralMap();if(this.next.isIdentifier())return this.parseAccessMember(new Y(this.span(e),this.sourceSpan(e)),e,!1);if(this.next.isNumber()){let n=this.next.toNumber();return this.advance(),new I(this.span(e),this.sourceSpan(e),n)}else{if(this.next.isTemplateLiteralEnd())return this.parseNoInterpolationTemplateLiteral();if(this.next.isTemplateLiteralPart())return this.parseTemplateLiteral();if(this.next.isString()&&this.next.kind===q.Plain){let n=this.next.toString();return this.advance(),new I(this.span(e),this.sourceSpan(e),n)}else return this.next.isPrivateIdentifier()?(this._reportErrorForPrivateIdentifier(this.next,null),new P(this.span(e),this.sourceSpan(e))):this.index>=this.tokens.length?(this.error(`Unexpected end of expression: ${this.input}`),new P(this.span(e),this.sourceSpan(e))):(this.error(`Unexpected token ${this.next}`),new P(this.span(e),this.sourceSpan(e)))}}}}parseExpressionList(e){let n=[];do if(!this.next.isCharacter(e))n.push(this.parsePipe());else break;while(this.consumeOptionalCharacter(Ce));return n}parseLiteralMap(){let e=[],n=[],s=this.inputIndex;if(this.expectCharacter(ot),!this.consumeOptionalCharacter(be)){this.rbracesExpected++;do{let r=this.inputIndex,i=this.next.isString(),a=this.expectIdentifierOrKeywordOrString(),l={key:a,quoted:i};if(e.push(l),i)this.expectCharacter(ie),n.push(this.parsePipe());else if(this.consumeOptionalCharacter(ie))n.push(this.parsePipe());else{l.isShorthandInitialized=!0;let h=this.span(r),f=this.sourceSpan(r);n.push(new le(h,f,f,new Y(h,f),a))}}while(this.consumeOptionalCharacter(Ce)&&!this.next.isCharacter(be));this.rbracesExpected--,this.expectCharacter(be)}return new Fe(this.span(s),this.sourceSpan(s),e,n)}parseAccessMember(e,n,s){let r=this.inputIndex,i=this.withContext(oe.Writable,()=>{let h=this.expectIdentifierOrKeyword()??"";return h.length===0&&this.error("Expected identifier for property access",e.span.end),h}),a=this.sourceSpan(r),l;if(s)this.consumeOptionalOperator("=")?(this.error("The '?.' operator cannot be used in the assignment"),l=new P(this.span(n),this.sourceSpan(n))):l=new ce(this.span(n),this.sourceSpan(n),a,e,i);else if(this.consumeOptionalOperator("=")){if(!(this.parseFlags&1))return this.error("Bindings cannot contain assignments"),new P(this.span(n),this.sourceSpan(n));let h=this.parseConditional();l=new $e(this.span(n),this.sourceSpan(n),a,e,i,h)}else l=new le(this.span(n),this.sourceSpan(n),a,e,i);return l}parseCall(e,n,s){let r=this.inputIndex;this.rparensExpected++;let i=this.parseCallArguments(),a=this.span(r,this.inputIndex).toAbsolute(this.absoluteOffset);this.expectCharacter(se),this.rparensExpected--;let l=this.span(n),h=this.sourceSpan(n);return s?new pe(l,h,e,i,a):new Ue(l,h,e,i,a)}parseCallArguments(){if(this.next.isCharacter(se))return[];let e=[];do e.push(this.parsePipe());while(this.consumeOptionalCharacter(Ce));return e}expectTemplateBindingKey(){let e="",n=!1,s=this.currentAbsoluteOffset;do e+=this.expectIdentifierOrKeywordOrString(),n=this.consumeOptionalOperator("-"),n&&(e+="-");while(n);return{source:e,span:new O(s,s+e.length)}}parseTemplateBindings(e){let n=[];for(n.push(...this.parseDirectiveKeywordBindings(e));this.index<this.tokens.length;){let s=this.parseLetBinding();if(s)n.push(s);else{let r=this.expectTemplateBindingKey(),i=this.parseAsBinding(r);i?n.push(i):(r.source=e.source+r.source.charAt(0).toUpperCase()+r.source.substring(1),n.push(...this.parseDirectiveKeywordBindings(r)))}this.consumeStatementTerminator()}return new Ft(n,[],this.errors)}parseKeyedReadOrWrite(e,n,s){return this.withContext(oe.Writable,()=>{this.rbracketsExpected++;let r=this.parsePipe();if(r instanceof P&&this.error("Key access cannot be empty"),this.rbracketsExpected--,this.expectCharacter(ke),this.consumeOptionalOperator("="))if(s)this.error("The '?.' operator cannot be used in the assignment");else{let i=this.parseConditional();return new De(this.span(n),this.sourceSpan(n),e,r,i)}else return s?new ue(this.span(n),this.sourceSpan(n),e,r):new Re(this.span(n),this.sourceSpan(n),e,r);return new P(this.span(n),this.sourceSpan(n))})}parseDirectiveKeywordBindings(e){let n=[];this.consumeOptionalCharacter(ie);let s=this.getDirectiveBoundTarget(),r=this.currentAbsoluteOffset,i=this.parseAsBinding(e);i||(this.consumeStatementTerminator(),r=this.currentAbsoluteOffset);let a=new O(e.span.start,r);return n.push(new We(a,e,s)),i&&n.push(i),n}getDirectiveBoundTarget(){if(this.next===et||this.peekKeywordAs()||this.peekKeywordLet())return null;let e=this.parsePipe(),{start:n,end:s}=e.span,r=this.input.substring(n,s);return new W(e,r,this.location,this.absoluteOffset+n,this.errors)}parseAsBinding(e){if(!this.peekKeywordAs())return null;this.advance();let n=this.expectTemplateBindingKey();this.consumeStatementTerminator();let s=new O(e.span.start,this.currentAbsoluteOffset);return new me(s,n,e)}parseLetBinding(){if(!this.peekKeywordLet())return null;let e=this.currentAbsoluteOffset;this.advance();let n=this.expectTemplateBindingKey(),s=null;this.consumeOptionalOperator("=")&&(s=this.expectTemplateBindingKey()),this.consumeStatementTerminator();let r=new O(e,this.currentAbsoluteOffset);return new me(r,n,s)}parseNoInterpolationTaggedTemplateLiteral(e,n){let s=this.parseNoInterpolationTemplateLiteral();return new he(this.span(n),this.sourceSpan(n),e,s)}parseNoInterpolationTemplateLiteral(){let e=this.next.strValue,n=this.inputIndex;this.advance();let s=this.span(n),r=this.sourceSpan(n);return new fe(s,r,[new de(s,r,e)],[])}parseTaggedTemplateLiteral(e,n){let s=this.parseTemplateLiteral();return new he(this.span(n),this.sourceSpan(n),e,s)}parseTemplateLiteral(){let e=[],n=[],s=this.inputIndex;for(;this.next!==et;){let r=this.next;if(r.isTemplateLiteralPart()||r.isTemplateLiteralEnd()){let i=this.inputIndex;if(this.advance(),e.push(new de(this.span(i),this.sourceSpan(i),r.strValue)),r.isTemplateLiteralEnd())break}else if(r.isTemplateLiteralInterpolationStart()){this.advance();let i=this.parsePipe();i instanceof P?this.error("Template literal interpolation cannot be empty"):n.push(i)}else this.advance()}return new fe(this.span(s),this.sourceSpan(s),e,n)}consumeStatementTerminator(){this.consumeOptionalCharacter(Te)||this.consumeOptionalCharacter(Ce)}error(e,n=null){this.errors.push(new Pe(e,this.input,this.locationText(n),this.location)),this.skip()}locationText(e=null){return e==null&&(e=this.index),e<this.tokens.length?`at column ${this.tokens[e].index+1} in`:"at the end of the expression"}_reportErrorForPrivateIdentifier(e,n){let s=`Private identifiers are not supported. Unexpected private identifier: ${e}`;n!==null&&(s+=`, ${n}`),this.error(s)}skip(){let e=this.next;for(;this.index<this.tokens.length&&!e.isCharacter(Te)&&!e.isOperator("|")&&(this.rparensExpected<=0||!e.isCharacter(se))&&(this.rbracesExpected<=0||!e.isCharacter(be))&&(this.rbracketsExpected<=0||!e.isCharacter(ke))&&(!(this.context&oe.Writable)||!e.isOperator("="));)this.next.isError()&&this.errors.push(new Pe(this.next.toString(),this.input,this.locationText(),this.location)),this.advance(),e=this.next}},Vt=class extends Lt{errors=[];visitPipe(){this.errors.push("pipes")}};function Hr(t){let e=new Map,n=0,s=0,r=0;for(;r<t.length;){let i=t[r];if(i.type===9){let[a,l]=i.parts;n+=l.length,s+=a.length}else{let a=i.parts.reduce((l,h)=>l+h.length,0);s+=a,n+=a}e.set(s,n),r++}return e}var Wr=new Map(Object.entries({class:"className",for:"htmlFor",formaction:"formAction",innerHtml:"innerHTML",readonly:"readOnly",tabindex:"tabIndex"})),Ii=Array.from(Wr).reduce((t,[e,n])=>(t.set(e,n),t),new Map);var Ni=new we(new qe);function B(t){return e=>e.kind===t}function Ne(t,e){return n=>n.kind===t&&e===n.expression instanceof at}function qr(t){return(t.kind===d.Property||t.kind===d.TwoWayProperty)&&!(t.expression instanceof at)}var Ai=[{test:B(d.StyleMap),transform:lt},{test:B(d.ClassMap),transform:lt},{test:B(d.StyleProp)},{test:B(d.ClassProp)},{test:Ne(d.Attribute,!0)},{test:Ne(d.Property,!0)},{test:qr},{test:Ne(d.Attribute,!1)}],Pi=[{test:Ne(d.DomProperty,!0)},{test:Ne(d.DomProperty,!1)},{test:B(d.Attribute)},{test:B(d.StyleMap),transform:lt},{test:B(d.ClassMap),transform:lt},{test:B(d.StyleProp)},{test:B(d.ClassProp)}],Li=new Set([d.Listener,d.TwoWayListener,d.StyleMap,d.ClassMap,d.StyleProp,d.ClassProp,d.Property,d.TwoWayProperty,d.DomProperty,d.Attribute]);function lt(t){return t.slice(t.length-1)}var Mi={constant:[p.interpolate,p.interpolate1,p.interpolate2,p.interpolate3,p.interpolate4,p.interpolate5,p.interpolate6,p.interpolate7,p.interpolate8],variable:p.interpolateV,mapping:t=>{if(t%2===0)throw new Error("Expected odd number of arguments");return(t-1)/2}};var $i=new Map([["window",p.resolveWindow],["document",p.resolveDocument],["body",p.resolveBody]]);var Ri=new Map([[D.HTML,p.sanitizeHtml],[D.RESOURCE_URL,p.sanitizeResourceUrl],[D.SCRIPT,p.sanitizeScript],[D.STYLE,p.sanitizeStyle],[D.URL,p.sanitizeUrl]]),Di=new Map([[D.HTML,p.trustConstantHtml],[D.RESOURCE_URL,p.trustConstantResourceUrl]]);var Xn;(function(t){t[t.None=0]="None",t[t.ViewContextRead=1]="ViewContextRead",t[t.ViewContextWrite=2]="ViewContextWrite",t[t.SideEffectful=4]="SideEffectful"})(Xn||(Xn={}));var Bi=new Map([[U.Property,H.Property],[U.TwoWay,H.TwoWayProperty],[U.Attribute,H.Attribute],[U.Class,H.ClassName],[U.Style,H.StyleProperty],[U.Animation,H.Animation]]);var Oi=Symbol("queryAdvancePlaceholder");var Jn;(function(t){t[t.NG_CONTENT=0]="NG_CONTENT",t[t.STYLE=1]="STYLE",t[t.STYLESHEET=2]="STYLESHEET",t[t.SCRIPT=3]="SCRIPT",t[t.OTHER=4]="OTHER"})(Jn||(Jn={}));var Yn;(function(t){t.IDLE="idle",t.TIMER="timer",t.INTERACTION="interaction",t.IMMEDIATE="immediate",t.HOVER="hover",t.VIEWPORT="viewport",t.NEVER="never"})(Yn||(Yn={}));var ms="%COMP%",Fi=`_nghost-${ms}`,Vi=`_ngcontent-${ms}`;var Qn;(function(t){t[t.Extract=0]="Extract",t[t.Merge=1]="Merge"})(Qn||(Qn={}));var Ui=new At("20.0.5");function gs({start:t,end:e},n){let s=t,r=e;for(;r!==s&&/\s/.test(n[r-1]);)r--;for(;s!==r&&/\s/.test(n[s]);)s++;return{start:s,end:r}}function zr({start:t,end:e},n){let s=t,r=e;for(;r!==n.length&&/\s/.test(n[r]);)r++;for(;s!==0&&/\s/.test(n[s-1]);)s--;return{start:s,end:r}}function Gr(t,e){return e[t.start-1]==="("&&e[t.end]===")"?{start:t.start-1,end:t.end+1}:t}function vs(t,e,n){let s=0,r={start:t.start,end:t.end};for(;;){let i=zr(r,e),a=Gr(i,e);if(i.start===a.start&&i.end===a.end)break;r.start=a.start,r.end=a.end,s++}return{hasParens:(n?s-1:s)!==0,outerSpan:gs(n?{start:r.start+1,end:r.end-1}:r,e),innerSpan:gs(t,e)}}function ws(t){return typeof t=="string"?e=>e===t:e=>t.test(e)}function xs(t,e,n){let s=ws(e);for(let r=n;r>=0;r--){let i=t[r];if(s(i))return r}throw new Error(`Cannot find front char ${e} from index ${n} in ${JSON.stringify(t)}`)}function Ss(t,e,n){let s=ws(e);for(let r=n;r<t.length;r++){let i=t[r];if(s(i))return r}throw new Error(`Cannot find character ${e} from index ${n} in ${JSON.stringify(t)}`)}function Es(t){return t.slice(0,1).toLowerCase()+t.slice(1)}function ze(t){let{start:e,end:n}=t;return{start:e,end:n,range:[e,n]}}var Xr=t=>we.prototype._commentStart(t);function Jr(t,e){let n=e?Xr(t):null;if(n===null)return{text:t,comments:[]};let s={type:"CommentLine",value:t.slice(n+2),...ze({start:n,end:t.length})};return{text:t.slice(0,n),comments:[s]}}function Ge(t,e=!0){return n=>{let s=new qe,r=new we(s),{text:i,comments:a}=Jr(n,e),l=t(i,r);if(l.errors.length!==0){let[{message:h}]=l.errors;throw new SyntaxError(h.replace(/^Parser Error: | at column \d+ in [^]*$/g,""))}return{result:l,comments:a,text:i}}}var ys=Ge((t,e)=>e.parseBinding(t,"",0)),Yr=Ge((t,e)=>e.parseSimpleBinding(t,"",0)),_s=Ge((t,e)=>e.parseAction(t,"",0)),Cs=Ge((t,e)=>e.parseInterpolationExpression(t,"",0)),Ts=Ge((t,e)=>e.parseTemplateBindings("",t,"",0,0),!1);var Kr=(t,e,n)=>{if(!(t&&e==null))return Array.isArray(e)||typeof e=="string"?e[n<0?e.length+n:n]:e.at(n)},ut=Kr;var jt=class{text;constructor(e){this.text=e}getCharacterIndex(e,n){return Ss(this.text,e,n)}getCharacterLastIndex(e,n){return xs(this.text,e,n)}transformSpan(e,{stripSpaces:n=!1,hasParentParens:s=!1}={}){if(!n)return ze(e);let{outerSpan:r,innerSpan:i,hasParens:a}=vs(e,this.text,s),l=ze(i);return a&&(l.extra={parenthesized:!0,parenStart:r.start,parenEnd:r.end}),l}createNode(e,{stripSpaces:n=!0,hasParentParens:s=!1}={}){let{type:r,start:i,end:a}=e,l={...e,...this.transformSpan({start:i,end:a},{stripSpaces:n,hasParentParens:s})};switch(r){case"NumericLiteral":case"StringLiteral":{let h=this.text.slice(l.start,l.end),{value:f}=l;l.extra={...l.extra,raw:h,rawValue:f};break}case"ObjectProperty":{let{shorthand:h}=l;h&&(l.extra={...l.extra,shorthand:h});break}}return l}},ks=jt;function zt(t){var e;return!!((e=t.extra)!=null&&e.parenthesized)}function $(t){return zt(t)?t.extra.parenStart:t.start}function R(t){return zt(t)?t.extra.parenEnd:t.end}function bs(t){return(t.type==="OptionalCallExpression"||t.type==="OptionalMemberExpression")&&!zt(t)}function Is(t,e){let{start:n,end:s}=t.sourceSpan;return n>=s||/^\s+$/.test(e.slice(n,s))}var Ye,xe,u,w,Xe,x,pt,Je=class extends ks{constructor(n,s){super(s);V(this,u);V(this,Ye);V(this,xe);te(this,Ye,n),te(this,xe,s)}get node(){return c(this,u,x).call(this,L(this,Ye))}transformNode(n){return c(this,u,pt).call(this,n)}};Ye=new WeakMap,xe=new WeakMap,u=new WeakSet,w=function(n,{stripSpaces:s=!0,hasParentParens:r=!1}={}){return this.createNode(n,{stripSpaces:s,hasParentParens:r})},Xe=function(n,s,{computed:r,optional:i,end:a=R(s),hasParentParens:l=!1}){if(Is(n,L(this,xe))||n.sourceSpan.start===s.start)return s;let h=c(this,u,x).call(this,n),f=bs(h);return c(this,u,w).call(this,{type:i||f?"OptionalMemberExpression":"MemberExpression",object:h,property:s,computed:r,...i?{optional:!0}:f?{optional:!1}:void 0,start:$(h),end:a},{hasParentParens:l})},x=function(n,s){return c(this,u,pt).call(this,n,s)},pt=function(n,s){let{isInParentParens:r}={isInParentParens:!1,...s};if(n instanceof Ut){let{expressions:i}=n;if(i.length!==1)throw new Error("Unexpected 'Interpolation'");return c(this,u,x).call(this,i[0])}if(n instanceof X)return c(this,u,w).call(this,{type:"UnaryExpression",prefix:!0,argument:c(this,u,x).call(this,n.expr),operator:n.operator,...n.sourceSpan},{hasParentParens:r});if(n instanceof A){let{left:i,operation:a,right:l}=n,h=c(this,u,x).call(this,i),f=c(this,u,x).call(this,l),v=$(h),E=R(f),y={left:h,right:f,start:v,end:E};return a==="&&"||a==="||"||a==="??"?c(this,u,w).call(this,{...y,type:"LogicalExpression",operator:a},{hasParentParens:r}):c(this,u,w).call(this,{...y,type:"BinaryExpression",operator:a},{hasParentParens:r})}if(n instanceof Be){let{exp:i,name:a,args:l}=n,h=c(this,u,x).call(this,i),f=$(h),v=R(h),E=this.getCharacterIndex(/\S/,this.getCharacterIndex("|",v)+1),y=c(this,u,w).call(this,{type:"Identifier",name:a,start:E,end:E+a.length}),T=l.map(k=>c(this,u,x).call(this,k));return c(this,u,w).call(this,{type:"NGPipeExpression",left:h,right:y,arguments:T,start:f,end:R(T.length===0?y:ut(!1,T,-1))},{hasParentParens:r})}if(n instanceof Le)return c(this,u,w).call(this,{type:"NGChainedExpression",expressions:n.expressions.map(i=>c(this,u,x).call(this,i)),...n.sourceSpan},{hasParentParens:r});if(n instanceof Me){let{condition:i,trueExp:a,falseExp:l}=n,h=c(this,u,x).call(this,i),f=c(this,u,x).call(this,a),v=c(this,u,x).call(this,l);return c(this,u,w).call(this,{type:"ConditionalExpression",test:h,consequent:f,alternate:v,start:$(h),end:R(v)},{hasParentParens:r})}if(n instanceof P)return c(this,u,w).call(this,{type:"NGEmptyExpression",...n.sourceSpan},{hasParentParens:r});if(n instanceof Y)return c(this,u,w).call(this,{type:"ThisExpression",...n.sourceSpan},{hasParentParens:r});if(n instanceof Re||n instanceof ue)return c(this,u,Xe).call(this,n.receiver,c(this,u,x).call(this,n.key),{computed:!0,optional:n instanceof ue,end:n.sourceSpan.end,hasParentParens:r});if(n instanceof Oe)return c(this,u,w).call(this,{type:"ArrayExpression",elements:n.expressions.map(i=>c(this,u,x).call(this,i)),...n.sourceSpan},{hasParentParens:r});if(n instanceof Fe){let{keys:i,values:a}=n,l=a.map(f=>c(this,u,x).call(this,f)),h=i.map(({key:f,quoted:v},E)=>{let y=l[E],T=$(y),k=R(y),F=this.getCharacterIndex(/\S/,E===0?n.sourceSpan.start+1:this.getCharacterIndex(",",R(l[E-1]))+1),Ee=T===F?k:this.getCharacterLastIndex(/\S/,this.getCharacterLastIndex(":",T-1)-1)+1,ye={start:F,end:Ee},j=v?c(this,u,w).call(this,{type:"StringLiteral",value:f,...ye}):c(this,u,w).call(this,{type:"Identifier",name:f,...ye}),en=j.end<j.start||F===T;return c(this,u,w).call(this,{type:"ObjectProperty",key:j,value:y,shorthand:en,computed:!1,start:$(j),end:k})});return c(this,u,w).call(this,{type:"ObjectExpression",properties:h,...n.sourceSpan},{hasParentParens:r})}if(n instanceof I){let{value:i}=n;switch(typeof i){case"boolean":return c(this,u,w).call(this,{type:"BooleanLiteral",value:i,...n.sourceSpan},{hasParentParens:r});case"number":return c(this,u,w).call(this,{type:"NumericLiteral",value:i,...n.sourceSpan},{hasParentParens:r});case"object":return c(this,u,w).call(this,{type:"NullLiteral",...n.sourceSpan},{hasParentParens:r});case"string":return c(this,u,w).call(this,{type:"StringLiteral",value:i,...n.sourceSpan},{hasParentParens:r});case"undefined":return c(this,u,w).call(this,{type:"Identifier",name:"undefined",...n.sourceSpan},{hasParentParens:r});default:throw new Error(`Unexpected LiteralPrimitive value type ${typeof i}`)}}if(n instanceof Ue||n instanceof pe){let i=n instanceof pe,{receiver:a,args:l}=n,h=l.length===1?[c(this,u,x).call(this,l[0],{isInParentParens:!0})]:l.map(y=>c(this,u,x).call(this,y)),f=c(this,u,x).call(this,a),v=bs(f),E=i||v?"OptionalCallExpression":"CallExpression";return c(this,u,w).call(this,{type:E,callee:f,arguments:h,...E==="OptionalCallExpression"?{optional:i}:void 0,start:$(f),end:n.sourceSpan.end},{hasParentParens:r})}if(n instanceof Ve){let i=c(this,u,x).call(this,n.expression);return c(this,u,w).call(this,{type:"TSNonNullExpression",expression:i,start:$(i),end:n.sourceSpan.end},{hasParentParens:r})}if(n instanceof Q||n instanceof K||n instanceof Z){let i=n instanceof Q?"!":n instanceof K?"typeof":n instanceof Z?"void":void 0;if(!i)throw new Error("Unexpected expression.");let{start:a}=n.sourceSpan;if(i==="typeof"||i==="void"){let h=this.text.lastIndexOf(i,a);if(h===-1)throw new Error(`Cannot find operator '${i}' from index ${a} in ${JSON.stringify(this.text)}`);a=h}let l=c(this,u,x).call(this,n.expression);return c(this,u,w).call(this,{type:"UnaryExpression",prefix:!0,operator:i,argument:l,start:a,end:R(l)},{hasParentParens:r})}if(n instanceof le||n instanceof ce){let{receiver:i,name:a}=n,l=this.getCharacterLastIndex(/\S/,n.sourceSpan.end-1)+1,h=c(this,u,w).call(this,{type:"Identifier",name:a,start:l-a.length,end:l},Is(i,L(this,xe))?{hasParentParens:r}:{});return c(this,u,Xe).call(this,i,h,{computed:!1,optional:n instanceof ce,hasParentParens:r})}if(n instanceof De){let i=c(this,u,x).call(this,n.key),a=c(this,u,x).call(this,n.value),l=c(this,u,Xe).call(this,n.receiver,i,{computed:!0,optional:!1,end:this.getCharacterIndex("]",R(i))+1});return c(this,u,w).call(this,{type:"AssignmentExpression",left:l,operator:"=",right:a,start:$(l),end:R(a)},{hasParentParens:r})}if(n instanceof $e){let{receiver:i,name:a,value:l}=n,h=c(this,u,x).call(this,l),f=this.getCharacterLastIndex(/\S/,this.getCharacterLastIndex("=",$(h)-1)-1)+1,v=c(this,u,w).call(this,{type:"Identifier",name:a,start:f-a.length,end:f}),E=c(this,u,Xe).call(this,i,v,{computed:!1,optional:!1});return c(this,u,w).call(this,{type:"AssignmentExpression",left:E,operator:"=",right:h,start:$(E),end:R(h)},{hasParentParens:r})}if(n instanceof he)return c(this,u,w).call(this,{type:"TaggedTemplateExpression",tag:c(this,u,x).call(this,n.tag),quasi:c(this,u,x).call(this,n.template),...n.sourceSpan});if(n instanceof fe){let{elements:i,expressions:a}=n;return c(this,u,w).call(this,{type:"TemplateLiteral",quasis:i.map(l=>c(this,u,x).call(this,l,{parent:n})),expressions:a.map(l=>c(this,u,x).call(this,l)),...n.sourceSpan})}if(n instanceof de){let{elements:i}=s.parent,a=i.indexOf(n),l=a===0,h=a===i.length-1,f=n.sourceSpan.end-(h?1:0),v=n.sourceSpan.start+(l?1:0),E=this.text.slice(v,f);return c(this,u,w).call(this,{type:"TemplateElement",value:{cooked:n.text,raw:E},start:v,end:f,tail:h},{stripSpaces:!1})}if(n instanceof He)return c(this,u,pt).call(this,n.expression);throw new Error(`Unexpected node type '${n.constructor.name}'`)};function Ns(t,e){return new Je(t,e).node}function As(t){return t instanceof We}function Ps(t){return t instanceof me}var Se,ee,g,Ls,N,Xt,Jt,Yt,Ms,$s,Rs,Ds,Gt=class extends Je{constructor(n,s){super(void 0,s);V(this,g);V(this,Se);V(this,ee);te(this,Se,n),te(this,ee,s);for(let r of n)c(this,g,Ms).call(this,r)}get expressions(){return c(this,g,Rs).call(this)}};Se=new WeakMap,ee=new WeakMap,g=new WeakSet,Ls=function(){return L(this,Se)[0].key},N=function(n,{stripSpaces:s=!0}={}){return this.createNode(n,{stripSpaces:s})},Xt=function(n){return this.transformNode(n)},Jt=function(n){return Es(n.slice(L(this,g,Ls).source.length))},Yt=function(n){let s=L(this,ee);if(s[n.start]!=='"'&&s[n.start]!=="'")return;let r=s[n.start],i=!1;for(let a=n.start+1;a<s.length;a++)switch(s[a]){case r:if(!i){n.end=a+1;return}default:i=!1;break;case"\\":i=!i;break}},Ms=function(n){c(this,g,Yt).call(this,n.key.span),Ps(n)&&n.value&&c(this,g,Yt).call(this,n.value.span)},$s=function(n){if(!n.value||n.value.source)return n.value;let s=this.getCharacterIndex(/\S/,n.sourceSpan.start);return{source:"$implicit",span:{start:s,end:s}}},Rs=function(){let n=L(this,Se),[s]=n,r=L(this,ee).slice(s.sourceSpan.start,s.sourceSpan.end).trim().length===0?n.slice(1):n,i=[],a=null;for(let[l,h]of r.entries()){if(a&&As(a)&&Ps(h)&&h.value&&h.value.source===a.key.source){let f=c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:h.key.source,...h.key.span}),v=(T,k)=>({...T,...this.transformSpan({start:T.start,end:k})}),E=T=>({...v(T,f.end),alias:f}),y=i.pop();if(y.type==="NGMicrosyntaxExpression")i.push(E(y));else if(y.type==="NGMicrosyntaxKeyedExpression"){let T=E(y.expression);i.push(v({...y,expression:T},T.end))}else throw new Error(`Unexpected type ${y.type}`)}else i.push(c(this,g,Ds).call(this,h,l));a=h}return c(this,g,N).call(this,{type:"NGMicrosyntax",body:i,...i.length===0?n[0].sourceSpan:{start:i[0].start,end:ut(!1,i,-1).end}})},Ds=function(n,s){if(As(n)){let{key:r,value:i}=n;return i?s===0?c(this,g,N).call(this,{type:"NGMicrosyntaxExpression",expression:c(this,g,Xt).call(this,i.ast),alias:null,...i.sourceSpan}):c(this,g,N).call(this,{type:"NGMicrosyntaxKeyedExpression",key:c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:c(this,g,Jt).call(this,r.source),...r.span}),expression:c(this,g,N).call(this,{type:"NGMicrosyntaxExpression",expression:c(this,g,Xt).call(this,i.ast),alias:null,...i.sourceSpan}),start:r.span.start,end:i.sourceSpan.end}):c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:c(this,g,Jt).call(this,r.source),...r.span})}else{let{key:r,sourceSpan:i}=n;if(/^let\s$/.test(L(this,ee).slice(i.start,i.start+4))){let{value:l}=n;return c(this,g,N).call(this,{type:"NGMicrosyntaxLet",key:c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:r.source,...r.span}),value:l?c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:l.source,...l.span}):null,start:i.start,end:l?l.span.end:r.span.end})}else{let l=c(this,g,$s).call(this,n);return c(this,g,N).call(this,{type:"NGMicrosyntaxAs",key:c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:l.source,...l.span}),alias:c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:r.source,...r.span}),start:l.span.start,end:r.span.end})}}};function Bs(t,e){return new Gt(t,e).expressions}function ht({result:{ast:t},text:e,comments:n}){return Object.assign(Ns(t,e),{comments:n})}function Os({result:{templateBindings:t},text:e}){return Bs(t,e)}var Fs=t=>ht(ys(t));var Vs=t=>ht(Cs(t)),Qt=t=>ht(_s(t)),Us=t=>Os(Ts(t));function Kt(t){var s,r,i;let e=((s=t.range)==null?void 0:s[0])??t.start,n=(i=((r=t.declaration)==null?void 0:r.decorators)??t.decorators)==null?void 0:i[0];return n?Math.min(Kt(n),e):e}function Hs(t){var n;return((n=t.range)==null?void 0:n[1])??t.end}function ft(t){return{astFormat:"estree",parse(e){let n=t(e);return{type:"NGRoot",node:t===Qt&&n.type!=="NGChainedExpression"?{...n,type:"NGChainedExpression",expressions:[n]}:n}},locStart:Kt,locEnd:Hs}}var Zr=ft(Qt),ei=ft(Fs),ti=ft(Vs),ni=ft(Us);return Xs(si);});